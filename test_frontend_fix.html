<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🔧 Frontend Fix Verification</h1>
    
    <div class="info status">
        <strong>Issue:</strong> "process is not defined" error in authService.js
        <br><strong>Fix:</strong> Replaced process.env.REACT_APP_API_URL with hardcoded URL
    </div>

    <div class="test-section">
        <h2>1. API Connection Test</h2>
        <p>Testing if the backend API is accessible:</p>
        <button onclick="testAPIConnection()">Test API Connection</button>
        <div id="api-result" class="log"></div>
    </div>

    <div class="test-section">
        <h2>2. Authentication Test</h2>
        <p>Testing authentication endpoints:</p>
        <button onclick="testAuth()">Test Authentication</button>
        <div id="auth-result" class="log"></div>
    </div>

    <div class="test-section">
        <h2>3. Frontend Access Test</h2>
        <p>Check if your React frontend is accessible:</p>
        <button onclick="testFrontend()">Test Frontend</button>
        <div id="frontend-result" class="log"></div>
    </div>

    <div class="test-section">
        <h2>4. Quick Fix Instructions</h2>
        <div class="info status">
            <strong>Steps to verify the fix:</strong><br>
            1. Refresh your browser at <a href="http://localhost:3000" target="_blank">http://localhost:3000</a><br>
            2. Or try <a href="http://**************:3000" target="_blank">http://**************:3000</a><br>
            3. Check browser console (F12) for any remaining errors<br>
            4. Try logging in with demo credentials
        </div>
    </div>

    <script>
        async function testAPIConnection() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.textContent = 'Testing API connection...';
            
            try {
                const response = await fetch('http://localhost:8000/api/health');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.textContent = `✅ API Connection Successful!\nStatus: ${data.status}\nAgents: ${data.agents_available?.length || 0}`;
                    resultDiv.parentElement.querySelector('button').style.backgroundColor = '#28a745';
                } else {
                    resultDiv.textContent = `❌ API Error: ${response.status} ${response.statusText}`;
                }
            } catch (error) {
                resultDiv.textContent = `❌ Connection Failed: ${error.message}\n\nMake sure your backend is running at http://localhost:8000`;
            }
        }

        async function testAuth() {
            const resultDiv = document.getElementById('auth-result');
            resultDiv.textContent = 'Testing authentication endpoints...';
            
            try {
                // Test login endpoint
                const loginResponse = await fetch('http://localhost:8000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'demo',
                        password: 'demo123'
                    })
                });

                if (loginResponse.ok) {
                    const loginData = await loginResponse.json();
                    resultDiv.textContent = `✅ Authentication Working!\nUser: ${loginData.user.username}\nToken: ${loginData.access_token.substring(0, 20)}...`;
                    resultDiv.parentElement.querySelector('button').style.backgroundColor = '#28a745';
                } else {
                    const errorData = await loginResponse.json();
                    resultDiv.textContent = `❌ Auth Error: ${loginResponse.status}\nDetails: ${errorData.detail || 'Unknown error'}`;
                }
            } catch (error) {
                resultDiv.textContent = `❌ Auth Test Failed: ${error.message}`;
            }
        }

        async function testFrontend() {
            const resultDiv = document.getElementById('frontend-result');
            resultDiv.textContent = 'Testing frontend accessibility...';
            
            try {
                // Test if React dev server is running
                const response = await fetch('http://localhost:3000');
                
                if (response.ok) {
                    resultDiv.textContent = `✅ Frontend Accessible!\nStatus: ${response.status}\nContent-Type: ${response.headers.get('content-type')}`;
                    resultDiv.parentElement.querySelector('button').style.backgroundColor = '#28a745';
                } else {
                    resultDiv.textContent = `❌ Frontend Error: ${response.status} ${response.statusText}`;
                }
            } catch (error) {
                resultDiv.textContent = `❌ Frontend Not Accessible: ${error.message}\n\nMake sure your React dev server is running:\nnpm start or yarn start`;
            }
        }

        // Auto-run API test on page load
        window.addEventListener('load', () => {
            setTimeout(testAPIConnection, 1000);
        });
    </script>
</body>
</html>
