"""
WebSocket endpoints for real-time communication.
"""
import json
from typing import Optional
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, Query
from sqlalchemy.orm import Session

from database import get_db
from models import Thread
from websocket_manager import manager, MessageTypes, create_websocket_message
from auth_service import AuthService

router = APIRouter()


@router.websocket("/ws/thread/{thread_id}")
async def websocket_thread_endpoint(
    websocket: WebSocket,
    thread_id: int,
    user_id: Optional[str] = Query(None),
    token: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """
    WebSocket endpoint for real-time thread communication.
    
    Supports:
    - Real-time message updates
    - Typing indicators
    - User presence
    - Agent status updates
    """
    # Validate thread exists
    thread = db.query(Thread).filter(Thread.id == thread_id).first()
    if not thread:
        await websocket.close(code=4004, reason="Thread not found")
        return
    
    # Optional authentication
    current_user = None
    if token:
        payload = AuthService.verify_token(token)
        if payload:
            username = payload.get("sub")
            if username:
                current_user = AuthService.get_user_by_username(db, username)
                user_id = username
    
    # Connect to WebSocket
    await manager.connect(websocket, thread_id=thread_id, user_id=user_id)
    
    # Send welcome message
    welcome_message = create_websocket_message(
        MessageTypes.SYSTEM,
        {
            "message": f"Connected to thread: {thread.title}",
            "thread_info": {
                "id": thread.id,
                "title": thread.title,
                "description": thread.description
            },
            "user": user_id,
            "authenticated": current_user is not None
        },
        thread_id
    )
    await manager.send_personal_message(json.dumps(welcome_message), websocket)
    
    # Notify others of user joining
    if user_id:
        join_message = create_websocket_message(
            MessageTypes.USER_JOINED,
            {
                "user": user_id,
                "message": f"{user_id} joined the conversation"
            },
            thread_id
        )
        await manager.send_to_thread(join_message, thread_id)
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            
            try:
                message_data = json.loads(data)
                message_type = message_data.get("type")
                payload = message_data.get("data", {})
                
                # Handle different message types
                if message_type == MessageTypes.USER_TYPING:
                    # Broadcast typing indicator
                    typing_message = create_websocket_message(
                        MessageTypes.USER_TYPING,
                        {
                            "user": user_id or "Anonymous",
                            "is_typing": payload.get("is_typing", False)
                        },
                        thread_id
                    )
                    await manager.send_to_thread(typing_message, thread_id)
                
                elif message_type == "ping":
                    # Respond to ping with pong
                    pong_message = create_websocket_message(
                        "pong",
                        {"timestamp": payload.get("timestamp")},
                        thread_id
                    )
                    await manager.send_personal_message(json.dumps(pong_message), websocket)
                
                elif message_type == "get_status":
                    # Send connection status
                    status_message = create_websocket_message(
                        "status",
                        {
                            "thread_id": thread_id,
                            "connected_users": manager.get_thread_connection_count(thread_id),
                            "total_connections": manager.get_total_connections()
                        },
                        thread_id
                    )
                    await manager.send_personal_message(json.dumps(status_message), websocket)
                
                else:
                    # Echo unknown message types
                    echo_message = create_websocket_message(
                        "echo",
                        {
                            "original_type": message_type,
                            "original_data": payload,
                            "message": "Message received"
                        },
                        thread_id
                    )
                    await manager.send_personal_message(json.dumps(echo_message), websocket)
                    
            except json.JSONDecodeError:
                # Handle invalid JSON
                error_message = create_websocket_message(
                    MessageTypes.ERROR,
                    {"message": "Invalid JSON format"},
                    thread_id
                )
                await manager.send_personal_message(json.dumps(error_message), websocket)
                
    except WebSocketDisconnect:
        # Handle disconnection
        manager.disconnect(websocket, thread_id=thread_id, user_id=user_id)
        
        # Notify others of user leaving
        if user_id:
            leave_message = create_websocket_message(
                MessageTypes.USER_LEFT,
                {
                    "user": user_id,
                    "message": f"{user_id} left the conversation"
                },
                thread_id
            )
            await manager.send_to_thread(leave_message, thread_id)


@router.websocket("/ws/global")
async def websocket_global_endpoint(
    websocket: WebSocket,
    user_id: Optional[str] = Query(None),
    token: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """
    Global WebSocket endpoint for system-wide notifications.
    
    Supports:
    - System announcements
    - Global status updates
    - Cross-thread notifications
    """
    # Optional authentication
    current_user = None
    if token:
        payload = AuthService.verify_token(token)
        if payload:
            username = payload.get("sub")
            if username:
                current_user = AuthService.get_user_by_username(db, username)
                user_id = username
    
    # Connect to global WebSocket
    await manager.connect(websocket, user_id=user_id)
    
    # Send welcome message
    welcome_message = {
        "type": "system",
        "data": {
            "message": "Connected to Collaborative Intelligence Platform",
            "user": user_id,
            "authenticated": current_user is not None,
            "features": [
                "Real-time messaging",
                "Multi-agent conversations", 
                "File sharing",
                "Conversation export"
            ]
        },
        "timestamp": "now"
    }
    await manager.send_personal_message(json.dumps(welcome_message), websocket)
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            
            try:
                message_data = json.loads(data)
                message_type = message_data.get("type")
                payload = message_data.get("data", {})
                
                if message_type == "ping":
                    # Respond to ping
                    pong_message = {
                        "type": "pong",
                        "data": {"timestamp": payload.get("timestamp")},
                        "timestamp": "now"
                    }
                    await manager.send_personal_message(json.dumps(pong_message), websocket)
                
                elif message_type == "get_stats":
                    # Send platform statistics
                    stats_message = {
                        "type": "stats",
                        "data": {
                            "total_connections": manager.get_total_connections(),
                            "active_threads": len(manager.active_connections),
                            "agents_available": 6
                        },
                        "timestamp": "now"
                    }
                    await manager.send_personal_message(json.dumps(stats_message), websocket)
                
            except json.JSONDecodeError:
                error_message = {
                    "type": "error",
                    "data": {"message": "Invalid JSON format"},
                    "timestamp": "now"
                }
                await manager.send_personal_message(json.dumps(error_message), websocket)
                
    except WebSocketDisconnect:
        manager.disconnect(websocket, user_id=user_id)
