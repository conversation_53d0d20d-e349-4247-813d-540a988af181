import React, { useState, useEffect, useRef } from 'react'
import { use<PERSON>ara<PERSON>, <PERSON> } from 'react-router-dom'
import { ArrowLeft, Send, Loader2, Paperclip, X } from 'lucide-react'
import { useThread } from '../context/ThreadContext'
import { useAuth } from '../context/AuthContext'
import { getThread, sendMessage } from '../services/api'
import MessageList from '../components/MessageList'
import MessageInput from '../components/MessageInput'
import AgentSelector from '../components/AgentSelector'
import FileUpload from '../components/FileUpload'
import FileList from '../components/FileList'
import UserPresence from '../components/UserPresence'
import MessageDebug from '../components/MessageDebug'
import authService from '../services/authService'

function ThreadView() {
  const { threadId } = useParams()
  const { currentThread, setCurrentThread, addMessageToThread } = useThread()
  const { user, getAuthHeaders } = useAuth()
  const [loading, setLoading] = useState(true)
  const [sending, setSending] = useState(false)
  const [error, setError] = useState(null)
  const [showFileUpload, setShowFileUpload] = useState(false)
  const [threadFiles, setThreadFiles] = useState([])
  const [websocket, setWebsocket] = useState(null)
  const [isTyping, setIsTyping] = useState(false)
  const messagesEndRef = useRef(null)
  const typingTimeoutRef = useRef(null)

  useEffect(() => {
    loadThread()
    setupWebSocket()

    return () => {
      if (websocket) {
        websocket.close()
      }
    }
  }, [threadId])

  useEffect(() => {
    scrollToBottom()
  }, [currentThread?.messages])

  const setupWebSocket = () => {
    if (!threadId) return

    try {
      const wsUrl = authService.createWebSocketUrl(`/ws/thread/${threadId}`)
      const ws = new WebSocket(wsUrl)

      ws.onopen = () => {
        console.log('WebSocket connected to thread', threadId)
        setWebsocket(ws)
      }

      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data)
          handleWebSocketMessage(message)
        } catch (error) {
          console.error('Error parsing WebSocket message:', error)
        }
      }

      ws.onclose = () => {
        console.log('WebSocket disconnected from thread', threadId)
        setWebsocket(null)
      }

      ws.onerror = (error) => {
        console.error('WebSocket error:', error)
      }

    } catch (error) {
      console.error('Failed to setup WebSocket:', error)
    }
  }

  const handleWebSocketMessage = (message) => {
    console.log('📨 WebSocket message received:', message)

    switch (message.type) {
      case 'new_message':
        // Add new message from other users or agents
        const newMessage = message.data
        console.log('📝 New message from WebSocket:', newMessage)

        if (newMessage && newMessage.id) {
          // Check if message already exists to prevent duplicates
          const existingMessage = currentThread?.messages?.find(
            msg => msg.id === newMessage.id ||
            (msg.content === newMessage.content && msg.author === newMessage.author && msg.author_type === newMessage.author_type)
          )

          if (!existingMessage) {
            console.log('➕ Adding message via WebSocket')
            addMessageToThread(parseInt(threadId), newMessage)
          } else {
            console.log('⚠️ Duplicate message detected, skipping:', newMessage)
            console.log('⚠️ Existing message:', existingMessage)
            console.log('⚠️ New message:', newMessage)
          }
        }
        break

      case 'user_joined':
        console.log('User joined:', message.data.user)
        break

      case 'user_left':
        console.log('User left:', message.data.user)
        break

      case 'system':
        console.log('System message:', message.data.message)
        break

      default:
        console.log('Unknown WebSocket message type:', message.type)
    }
  }

  const loadThread = async () => {
    try {
      setLoading(true)
      setError(null)
      const thread = await getThread(threadId)
      setCurrentThread(thread)

      // Load thread files
      loadThreadFiles()
    } catch (error) {
      console.error('Error loading thread:', error)
      setError('Failed to load conversation')
    } finally {
      setLoading(false)
    }
  }

  const loadThreadFiles = async () => {
    try {
      const response = await fetch(`/api/files/thread/${threadId}`)
      if (response.ok) {
        const files = await response.json()
        setThreadFiles(files)
      }
    } catch (error) {
      console.error('Failed to load thread files:', error)
    }
  }

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleSendMessage = async (content, author = null) => {
    if (!content.trim() || sending) return

    try {
      setSending(true)
      setError(null)

      // Use authenticated user's name if available
      const messageAuthor = author || (user ? user.username : 'User')

      // Add user message to UI immediately for better UX
      const userMessage = {
        id: Date.now(), // Temporary ID
        content,
        author: messageAuthor,
        author_type: 'human',
        created_at: new Date().toISOString(),
        user_id: user?.id
      }

      addMessageToThread(parseInt(threadId), userMessage)

      // Send to API with authentication headers (using Vite proxy)
      const response = await fetch(`/api/threads/${threadId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders()
        },
        body: JSON.stringify({
          content,
          author: messageAuthor
        })
      })

      if (!response.ok) {
        throw new Error('Failed to send message')
      }

      const result = await response.json()

      console.log('🔄 API Response received:', result)
      console.log('🔌 WebSocket connected:', !!websocket)

      // IMPORTANT: Never add agent response here when WebSocket is connected
      // The WebSocket will handle adding the agent response to avoid duplicates
      if (!websocket && result.content && result.author) {
        console.log('➕ Adding agent response via API (no WebSocket)')
        const agentMessage = {
          id: Date.now() + 1, // Temporary ID
          content: result.content,
          author: result.author,
          author_type: result.author_type || 'agent',
          created_at: new Date().toISOString()
        }

        addMessageToThread(parseInt(threadId), agentMessage)
      } else if (websocket) {
        console.log('⏳ WebSocket connected - agent response will come via WebSocket')
        console.log('⏳ NOT adding agent response via API to prevent duplicates')
      } else {
        console.log('⚠️ No WebSocket and no agent response content')
      }

    } catch (error) {
      console.error('Error sending message:', error)
      setError('Failed to send message. Please try again.')
    } finally {
      setSending(false)
    }
  }

  const handleTyping = (isTyping) => {
    if (!websocket || !user) return

    setIsTyping(isTyping)

    // Send typing indicator via WebSocket
    const typingMessage = {
      type: 'user_typing',
      data: {
        is_typing: isTyping
      }
    }

    websocket.send(JSON.stringify(typingMessage))

    // Clear typing indicator after a delay
    if (isTyping) {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current)
      }

      typingTimeoutRef.current = setTimeout(() => {
        handleTyping(false)
      }, 3000)
    }
  }

  const handleFileUploaded = (fileInfo) => {
    // Add the new file to the list
    setThreadFiles(prev => [...prev, fileInfo])
    setShowFileUpload(false)

    // Show success message
    console.log('File uploaded successfully:', fileInfo)
  }

  const handleFileDeleted = (fileId) => {
    setThreadFiles(prev => prev.filter(f => f.id !== fileId))
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-3">
          <Loader2 className="w-6 h-6 animate-spin text-primary-600" />
          <span className="text-gray-600">Loading conversation...</span>
        </div>
      </div>
    )
  }

  if (error && !currentThread) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">{error}</div>
        <Link to="/" className="btn-primary">
          Back to Dashboard
        </Link>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Link
            to="/"
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Dashboard</span>
          </Link>
        </div>

        <div className="flex items-center space-x-3">
          <button
            onClick={() => setShowFileUpload(!showFileUpload)}
            className="flex items-center space-x-2 px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <Paperclip className="w-4 h-4" />
            <span>Upload File</span>
          </button>
        </div>
      </div>

      {currentThread && (
        <>
          {/* Thread Info */}
          <div className="card p-6 mb-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              {currentThread.title}
            </h1>
            {currentThread.description && (
              <p className="text-gray-600 mb-4">{currentThread.description}</p>
            )}
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <span>
                {currentThread.messages?.length || 0} messages
              </span>
              <span>•</span>
              <span>
                {threadFiles.length} files
              </span>
              <span>•</span>
              <span>
                Created {new Date(currentThread.created_at).toLocaleDateString()}
              </span>
              {currentThread.updated_at !== currentThread.created_at && (
                <>
                  <span>•</span>
                  <span>
                    Updated {new Date(currentThread.updated_at).toLocaleDateString()}
                  </span>
                </>
              )}
            </div>
          </div>

          {/* File Upload */}
          {showFileUpload && (
            <div className="card p-6 mb-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Upload File</h3>
                <button
                  onClick={() => setShowFileUpload(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
              <FileUpload
                threadId={parseInt(threadId)}
                onFileUploaded={handleFileUploaded}
              />
            </div>
          )}

          {/* Files List */}
          {threadFiles.length > 0 && (
            <div className="card p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Uploaded Files ({threadFiles.length})
              </h3>
              <FileList
                files={threadFiles}
                onFileDeleted={handleFileDeleted}
              />
            </div>
          )}

          {/* Messages */}
          <div className="card mb-6">
            {/* User Presence */}
            <UserPresence threadId={parseInt(threadId)} websocket={websocket} />

            <div className="h-96 overflow-y-auto p-6">
              {/* Debug component - remove after fixing */}
              <MessageDebug messages={currentThread.messages} threadId={threadId} />

              {currentThread.messages && currentThread.messages.length > 0 ? (
                <>
                  <MessageList messages={currentThread.messages} />
                  <div ref={messagesEndRef} />
                </>
              ) : (
                <div className="text-center py-12">
                  <p className="text-gray-500 mb-4">
                    No messages yet. Start the conversation!
                  </p>
                  <AgentSelector onSelect={(agent) => {
                    // Auto-fill message input with agent mention
                    const input = document.querySelector('#message-input')
                    if (input) {
                      input.value = `@${agent.name} `
                      input.focus()
                    }
                  }} />
                </div>
              )}
            </div>
          </div>

          {/* Message Input */}
          <div className="card p-4">
            {error && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
                {error}
              </div>
            )}
            <MessageInput
              onSend={handleSendMessage}
              onTyping={handleTyping}
              disabled={sending}
              placeholder="Type your message or use @mention to route to specific agents..."
            />
            {sending && (
              <div className="flex items-center space-x-2 mt-2 text-sm text-gray-600">
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>Sending message and waiting for agent response...</span>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  )
}

export default ThreadView
