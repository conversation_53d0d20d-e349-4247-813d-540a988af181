/**
 * Authentication service for user management and session handling.
 */

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000'

class AuthService {
  constructor() {
    this.token = localStorage.getItem('auth_token')
    this.user = JSON.parse(localStorage.getItem('auth_user') || 'null')
    this.listeners = new Set()
  }

  /**
   * Add listener for authentication state changes
   */
  addAuthListener(callback) {
    this.listeners.add(callback)
  }

  /**
   * Remove authentication state listener
   */
  removeAuthListener(callback) {
    this.listeners.delete(callback)
  }

  /**
   * Notify all listeners of authentication state change
   */
  notifyListeners() {
    this.listeners.forEach(callback => callback(this.user, this.token))
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated() {
    return !!(this.token && this.user)
  }

  /**
   * Get current user
   */
  getCurrentUser() {
    return this.user
  }

  /**
   * Get authentication token
   */
  getToken() {
    return this.token
  }

  /**
   * Login with username and password
   */
  async login(username, password) {
    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.detail || 'Login failed')
      }

      const data = await response.json()
      
      // Store authentication data
      this.token = data.access_token
      this.user = data.user
      
      localStorage.setItem('auth_token', this.token)
      localStorage.setItem('auth_user', JSON.stringify(this.user))
      
      // Notify listeners
      this.notifyListeners()
      
      return { success: true, user: this.user }
    } catch (error) {
      console.error('Login error:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * Register a new user
   */
  async register(username, email = null, fullName = null) {
    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username,
          email,
          full_name: fullName,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.detail || 'Registration failed')
      }

      const user = await response.json()
      
      // Auto-login after registration
      const loginResult = await this.login(username, 'demo123')
      
      return loginResult
    } catch (error) {
      console.error('Registration error:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * Logout current user
   */
  logout() {
    this.token = null
    this.user = null
    
    localStorage.removeItem('auth_token')
    localStorage.removeItem('auth_user')
    
    // Notify listeners
    this.notifyListeners()
  }

  /**
   * Quick login for demo users
   */
  async quickLogin(username) {
    return await this.login(username, 'demo123')
  }

  /**
   * Get authentication headers for API requests
   */
  getAuthHeaders() {
    if (!this.token) {
      return {}
    }
    
    return {
      'Authorization': `Bearer ${this.token}`
    }
  }

  /**
   * Make authenticated API request
   */
  async authenticatedFetch(url, options = {}) {
    const headers = {
      'Content-Type': 'application/json',
      ...this.getAuthHeaders(),
      ...options.headers,
    }

    const response = await fetch(url, {
      ...options,
      headers,
    })

    // Handle token expiration
    if (response.status === 401) {
      this.logout()
      throw new Error('Authentication expired. Please login again.')
    }

    return response
  }

  /**
   * Get demo users for quick access
   */
  getDemoUsers() {
    return [
      { username: 'demo', displayName: 'Demo User', color: '#3B82F6' },
      { username: 'alice', displayName: 'Alice Johnson', color: '#10B981' },
      { username: 'bob', displayName: 'Bob Smith', color: '#F59E0B' },
      { username: 'carol', displayName: 'Carol Davis', color: '#EF4444' },
      { username: 'david', displayName: 'David Wilson', color: '#8B5CF6' },
    ]
  }

  /**
   * Get user display info
   */
  getUserDisplayInfo(username) {
    const demoUsers = this.getDemoUsers()
    const demoUser = demoUsers.find(u => u.username === username)
    
    if (demoUser) {
      return demoUser
    }
    
    // Generate consistent color for unknown users
    const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#EC4899', '#06B6D4']
    const colorIndex = username.length % colors.length
    
    return {
      username,
      displayName: username.charAt(0).toUpperCase() + username.slice(1),
      color: colors[colorIndex]
    }
  }

  /**
   * Create WebSocket connection with authentication
   */
  createWebSocketUrl(path) {
    const wsBase = API_BASE_URL.replace('http', 'ws')
    const params = new URLSearchParams()
    
    if (this.user) {
      params.append('user_id', this.user.username)
    }
    
    if (this.token) {
      params.append('token', this.token)
    }
    
    return `${wsBase}${path}?${params.toString()}`
  }
}

// Create singleton instance
const authService = new AuthService()

export default authService
