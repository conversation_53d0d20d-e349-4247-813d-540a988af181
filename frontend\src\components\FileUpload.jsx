import React, { useState, useRef } from 'react'
import { Upload, File, X, CheckCircle, AlertCircle, Loader2 } from 'lucide-react'

function FileUpload({ threadId, onFileUploaded, className = "" }) {
  const [isDragging, setIsDragging] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [uploadStatus, setUploadStatus] = useState(null)
  const fileInputRef = useRef(null)

  const handleDragOver = (e) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = (e) => {
    e.preventDefault()
    setIsDragging(false)
  }

  const handleDrop = (e) => {
    e.preventDefault()
    setIsDragging(false)
    
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileUpload(files[0])
    }
  }

  const handleFileSelect = (e) => {
    const files = Array.from(e.target.files)
    if (files.length > 0) {
      handleFileUpload(files[0])
    }
  }

  const handleFileUpload = async (file) => {
    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024
    if (file.size > maxSize) {
      setUploadStatus({
        type: 'error',
        message: 'File size exceeds 10MB limit'
      })
      return
    }

    // Validate file type
    const allowedTypes = ['pdf', 'docx', 'txt', 'md', 'py', 'js', 'json', 'csv']
    const fileExtension = file.name.split('.').pop().toLowerCase()
    
    if (!allowedTypes.includes(fileExtension)) {
      setUploadStatus({
        type: 'error',
        message: `File type .${fileExtension} not supported. Allowed: ${allowedTypes.join(', ')}`
      })
      return
    }

    setUploading(true)
    setUploadStatus(null)

    try {
      const formData = new FormData()
      formData.append('file', file)
      
      if (threadId) {
        formData.append('thread_id', threadId)
      }

      const response = await fetch('/api/files/upload', {
        method: 'POST',
        body: formData
      })

      if (response.ok) {
        const result = await response.json()
        setUploadStatus({
          type: 'success',
          message: `File uploaded successfully! Extracted ${result.text_length} characters.`,
          fileInfo: result
        })
        
        if (onFileUploaded) {
          onFileUploaded(result)
        }
      } else {
        const error = await response.json()
        setUploadStatus({
          type: 'error',
          message: error.detail || 'Upload failed'
        })
      }
    } catch (error) {
      setUploadStatus({
        type: 'error',
        message: 'Network error during upload'
      })
    } finally {
      setUploading(false)
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const clearStatus = () => {
    setUploadStatus(null)
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          isDragging
            ? 'border-primary-500 bg-primary-50'
            : 'border-gray-300 hover:border-gray-400'
        } ${uploading ? 'opacity-50 pointer-events-none' : ''}`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          onChange={handleFileSelect}
          accept=".pdf,.docx,.txt,.md,.py,.js,.json,.csv"
          className="hidden"
        />

        {uploading ? (
          <div className="flex flex-col items-center space-y-2">
            <Loader2 className="w-8 h-8 text-primary-600 animate-spin" />
            <p className="text-gray-600">Uploading and processing file...</p>
          </div>
        ) : (
          <div className="flex flex-col items-center space-y-2">
            <Upload className="w-8 h-8 text-gray-400" />
            <div>
              <p className="text-gray-600">
                Drag and drop a file here, or{' '}
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="text-primary-600 hover:text-primary-700 font-medium"
                >
                  browse
                </button>
              </p>
              <p className="text-sm text-gray-500 mt-1">
                Supports: PDF, DOCX, TXT, MD, code files (max 10MB)
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Status Messages */}
      {uploadStatus && (
        <div
          className={`p-4 rounded-lg flex items-start space-x-3 ${
            uploadStatus.type === 'success'
              ? 'bg-green-50 border border-green-200'
              : 'bg-red-50 border border-red-200'
          }`}
        >
          {uploadStatus.type === 'success' ? (
            <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5" />
          ) : (
            <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" />
          )}
          
          <div className="flex-1">
            <p
              className={`text-sm ${
                uploadStatus.type === 'success' ? 'text-green-800' : 'text-red-800'
              }`}
            >
              {uploadStatus.message}
            </p>
            
            {uploadStatus.fileInfo && (
              <div className="mt-2 text-xs text-green-700">
                <p>File: {uploadStatus.fileInfo.filename}</p>
                <p>Type: {uploadStatus.fileInfo.file_type}</p>
                <p>Size: {(uploadStatus.fileInfo.file_size / 1024).toFixed(1)} KB</p>
              </div>
            )}
          </div>
          
          <button
            onClick={clearStatus}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      )}

      {/* Supported File Types */}
      <div className="text-xs text-gray-500">
        <p className="font-medium mb-1">Supported file types:</p>
        <div className="flex flex-wrap gap-2">
          {['PDF', 'DOCX', 'TXT', 'MD', 'PY', 'JS', 'JSON', 'CSV'].map((type) => (
            <span
              key={type}
              className="px-2 py-1 bg-gray-100 rounded text-gray-700"
            >
              {type}
            </span>
          ))}
        </div>
      </div>
    </div>
  )
}

export default FileUpload
