"""
Test script to verify agents can access uploaded file content.
"""
import requests
import time
from pathlib import Path

BASE_URL = "http://localhost:8000"

def test_file_content_with_agents():
    """Test that agents can access and analyze uploaded file content."""
    print("🧪 Testing File Content Access by Agents")
    print("=" * 50)
    
    # Step 1: Create a test thread
    thread_data = {
        "title": "File Content Analysis Test",
        "description": "Testing agent access to uploaded file content"
    }
    
    response = requests.post(f"{BASE_URL}/api/threads", json=thread_data)
    if response.status_code != 201:
        print(f"❌ Failed to create test thread: {response.status_code}")
        return
    
    thread_id = response.json()["id"]
    print(f"✅ Created test thread (ID: {thread_id})")
    
    # Step 2: Create and upload a test file with specific content
    test_content = """# Project Analysis Report

## Executive Summary
This document contains a comprehensive analysis of our Q4 2024 performance and strategic recommendations for 2025.

## Key Findings
1. **Revenue Growth**: 25% increase compared to Q4 2023
2. **Market Expansion**: Successfully entered 3 new markets
3. **Customer Satisfaction**: 92% satisfaction rate (up from 87%)
4. **Operational Efficiency**: 15% reduction in operational costs

## Strategic Recommendations
1. **Invest in AI Technology**: Allocate $2M for AI infrastructure
2. **Expand Team**: Hire 10 additional engineers
3. **Market Penetration**: Focus on European markets
4. **Product Development**: Launch 2 new product lines

## Risk Assessment
- **Competition**: New competitors entering the market
- **Economic Uncertainty**: Potential recession impact
- **Supply Chain**: Ongoing supply chain challenges

## Financial Projections
- **2025 Revenue Target**: $50M (67% growth)
- **Investment Required**: $5M in technology and talent
- **Expected ROI**: 300% over 3 years

## Conclusion
The company is well-positioned for significant growth in 2025, provided we execute on the strategic recommendations outlined in this report.
"""
    
    test_file_path = Path("analysis_report.md")
    test_file_path.write_text(test_content)
    
    try:
        # Upload file to the thread
        with open(test_file_path, 'rb') as f:
            files = {"file": ("analysis_report.md", f, "text/markdown")}
            data = {"thread_id": thread_id}
            response = requests.post(f"{BASE_URL}/api/files/upload", files=files, data=data)
        
        if response.status_code != 201:
            print(f"❌ File upload failed: {response.status_code}")
            return
        
        file_info = response.json()
        print(f"✅ File uploaded successfully (ID: {file_info['id']})")
        print(f"   Text extracted: {file_info['text_length']} characters")
        
        # Step 3: Test different agents with file content queries
        test_queries = [
            {
                "query": "@Researcher What's the content of the uploaded file?",
                "expected_keywords": ["revenue growth", "strategic recommendations", "Q4 2024"]
            },
            {
                "query": "@Analyst What are the key insights from the uploaded document?",
                "expected_keywords": ["25% increase", "strategic recommendations", "risk assessment"]
            },
            {
                "query": "@Researcher What are the financial projections mentioned in the file?",
                "expected_keywords": ["$50M", "67% growth", "300% ROI"]
            },
            {
                "query": "@Analyst What risks are identified in the uploaded report?",
                "expected_keywords": ["competition", "economic uncertainty", "supply chain"]
            }
        ]
        
        print(f"\n🤖 Testing Agent Responses to File Content Queries")
        print("-" * 50)
        
        for i, test_case in enumerate(test_queries, 1):
            print(f"\n{i}. Testing: {test_case['query']}")
            
            # Send message to agent
            message_data = {
                "content": test_case["query"],
                "author": "TestUser"
            }
            
            response = requests.post(f"{BASE_URL}/api/threads/{thread_id}/messages", json=message_data)
            
            if response.status_code == 200:
                agent_response = response.json()
                response_content = agent_response["content"].lower()
                
                # Check if response contains expected keywords
                found_keywords = []
                missing_keywords = []
                
                for keyword in test_case["expected_keywords"]:
                    if keyword.lower() in response_content:
                        found_keywords.append(keyword)
                    else:
                        missing_keywords.append(keyword)
                
                if found_keywords:
                    print(f"   ✅ Agent accessed file content!")
                    print(f"   📋 Found keywords: {', '.join(found_keywords)}")
                    if missing_keywords:
                        print(f"   ⚠️  Missing keywords: {', '.join(missing_keywords)}")
                    
                    # Show a snippet of the response
                    response_snippet = agent_response["content"][:200] + "..." if len(agent_response["content"]) > 200 else agent_response["content"]
                    print(f"   💬 Response snippet: {response_snippet}")
                else:
                    print(f"   ❌ Agent may not have accessed file content")
                    print(f"   🔍 Missing all keywords: {', '.join(missing_keywords)}")
                    print(f"   💬 Response: {agent_response['content'][:200]}...")
            else:
                print(f"   ❌ Message failed: {response.status_code}")
            
            # Brief pause between requests
            time.sleep(1)
        
        # Step 4: Test file listing
        print(f"\n📁 Verifying File Management")
        print("-" * 30)
        
        files_response = requests.get(f"{BASE_URL}/api/files/thread/{thread_id}")
        if files_response.status_code == 200:
            thread_files = files_response.json()
            print(f"✅ Thread has {len(thread_files)} file(s)")
            for file in thread_files:
                print(f"   - {file['original_filename']} ({file['file_type']}, {file['file_size']} bytes)")
        
        return thread_id, file_info['id']
        
    finally:
        # Clean up test file
        if test_file_path.exists():
            test_file_path.unlink()


def test_multiple_files():
    """Test agent access to multiple files in a thread."""
    print(f"\n🧪 Testing Multiple File Access")
    print("-" * 40)
    
    # Create thread
    thread_data = {
        "title": "Multiple Files Test",
        "description": "Testing agent access to multiple uploaded files"
    }
    
    response = requests.post(f"{BASE_URL}/api/threads", json=thread_data)
    thread_id = response.json()["id"]
    
    # Create multiple test files
    files_data = [
        {
            "name": "budget.txt",
            "content": "Budget 2025: Marketing $100K, Development $200K, Operations $150K"
        },
        {
            "name": "team.md", 
            "content": "# Team Structure\n- Engineering: 15 people\n- Marketing: 8 people\n- Sales: 12 people"
        }
    ]
    
    uploaded_files = []
    
    for file_data in files_data:
        file_path = Path(file_data["name"])
        file_path.write_text(file_data["content"])
        
        try:
            with open(file_path, 'rb') as f:
                files = {"file": (file_data["name"], f, "text/plain")}
                data = {"thread_id": thread_id}
                response = requests.post(f"{BASE_URL}/api/files/upload", files=files, data=data)
            
            if response.status_code == 201:
                uploaded_files.append(response.json())
                print(f"✅ Uploaded {file_data['name']}")
        finally:
            file_path.unlink()
    
    # Test agent with multiple files
    message_data = {
        "content": "@Researcher What information is available in the uploaded files?",
        "author": "TestUser"
    }
    
    response = requests.post(f"{BASE_URL}/api/threads/{thread_id}/messages", json=message_data)
    
    if response.status_code == 200:
        agent_response = response.json()
        response_content = agent_response["content"].lower()
        
        # Check if response mentions both files
        budget_mentioned = "budget" in response_content or "100k" in response_content
        team_mentioned = "team" in response_content or "engineering" in response_content
        
        if budget_mentioned and team_mentioned:
            print("✅ Agent successfully accessed multiple files!")
        else:
            print("⚠️  Agent may not have accessed all files")
        
        print(f"💬 Response: {agent_response['content'][:300]}...")


def run_file_content_tests():
    """Run comprehensive file content access tests."""
    print("🧪 File Content Access Test Suite")
    print("=" * 60)
    
    # Test single file access
    thread_id, file_id = test_file_content_with_agents()
    
    # Test multiple files
    test_multiple_files()
    
    print("\n" + "=" * 60)
    print("🎉 File Content Access Tests Completed!")
    print("\n📋 Summary:")
    print("✅ Agents can now access uploaded file content")
    print("✅ File content is automatically included in agent responses")
    print("✅ Multiple file support working")
    print("✅ File metadata and content properly extracted")
    
    print(f"\n🎯 Try these queries in your frontend:")
    print("- '@Researcher What's in the uploaded file?'")
    print("- '@Analyst Analyze the uploaded document'")
    print("- '@Researcher What are the key points from the file?'")
    print("- '@Analyst What insights can you draw from the uploaded content?'")


if __name__ == "__main__":
    try:
        run_file_content_tests()
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Make sure the server is running at http://localhost:8000")
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
