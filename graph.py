"""
LangGraph multi-agent system for collaborative intelligence platform.

This module implements the core conversational graph with:
- State management for persistent conversations
- Agent nodes (<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Summarizer)
- @mention routing logic
- Azure OpenAI integration
"""
import os
import re
from typing import Annotated, List, Literal, Optional, TypedDict
from typing_extensions import TypedDict
from dotenv import load_dotenv

from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain_openai import AzureChatOpenAI
from langchain_community.tools import TavilySearchResults
from langgraph.graph import StateGraph, START
from langgraph.graph.message import add_messages
from langgraph.types import Command
from pydantic import BaseModel, Field

# Load environment variables
load_dotenv()


# ============================================================================
# STATE DEFINITION
# ============================================================================

class ConversationState(TypedDict):
    """
    State schema for the conversation graph.
    
    This state persists throughout the conversation and contains:
    - messages: Full conversation history with automatic message aggregation
    - thread_id: Database thread identifier for persistence
    - last_agent: Track which agent was last active for routing
    - mentioned_agent: Which agent was @mentioned in the latest message
    """
    messages: Annotated[List[BaseMessage], add_messages]
    thread_id: int
    last_agent: Optional[str]
    mentioned_agent: Optional[str]


# ============================================================================
# AGENT CONFIGURATION
# ============================================================================

# Initialize Azure OpenAI client with error handling
def create_azure_llm():
    """Create Azure OpenAI client with proper error handling."""
    required_env_vars = {
        "AZURE_OPENAI_API_KEY": os.getenv("AZURE_OPENAI_API_KEY"),
        "AZURE_OPENAI_ENDPOINT": os.getenv("AZURE_OPENAI_ENDPOINT"),
    }

    missing_vars = [var for var, value in required_env_vars.items() if not value]
    if missing_vars:
        raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")

    return AzureChatOpenAI(
        azure_deployment=os.getenv("AZURE_OPENAI_DEPLOYMENT", "gpt-4"),
        api_version=os.getenv("AZURE_OPENAI_API_VERSION", "2024-02-15-preview"),
        temperature=0.7,
        max_tokens=1000,
    )

# Initialize the Azure OpenAI client
azure_llm = create_azure_llm()

# Initialize Tavily search tool for the researcher
def create_search_tool():
    """Create Tavily search tool with proper error handling."""
    tavily_api_key = os.getenv("TAVILY_API_KEY")
    if not tavily_api_key:
        raise ValueError("Missing required environment variable: TAVILY_API_KEY")

    return TavilySearchResults(
        max_results=5,
        search_depth="advanced",
        api_key=tavily_api_key
    )

# Initialize the search tool
search_tool = create_search_tool()


# ============================================================================
# AGENT NODES
# ============================================================================

def research_agent(state: ConversationState) -> Command[Literal["router"]]:
    """
    Research Agent Node
    
    Specializes in finding and gathering information using web search.
    Uses Tavily Search API to find relevant, up-to-date information.
    """
    messages = state["messages"]
    latest_message = messages[-1]
    
    # Extract the user's query (remove @mention)
    query = re.sub(r'@\w+\s*', '', latest_message.content).strip()
    
    try:
        # Perform web search
        search_results = search_tool.invoke({"query": query})
        
        # Format search results
        formatted_results = "\n\n".join([
            f"**{result.get('title', 'No title')}**\n{result.get('content', 'No content')}\nSource: {result.get('url', 'No URL')}"
            for result in search_results
        ])
        
        # Generate response using Azure OpenAI
        system_prompt = SystemMessage(content="""
        You are a Research Agent specializing in finding and synthesizing information.
        
        Based on the search results provided, create a comprehensive response that:
        1. Directly answers the user's question
        2. Cites specific sources and data points
        3. Highlights key findings and trends
        4. Suggests areas for further investigation if relevant
        
        Be factual, cite your sources, and present information clearly.
        """)
        
        human_prompt = HumanMessage(content=f"""
        User Query: {query}
        
        Search Results:
        {formatted_results}
        
        Please provide a comprehensive research-based response.
        """)
        
        response = azure_llm.invoke([system_prompt, human_prompt])
        
        # Create agent response message
        agent_message = AIMessage(
            content=response.content,
            name="Researcher"
        )
        
    except Exception as e:
        # Fallback response if search fails
        agent_message = AIMessage(
            content=f"I apologize, but I encountered an issue while researching your query: {str(e)}. Please try rephrasing your question or check back later.",
            name="Researcher"
        )
    
    return Command(
        goto="router",
        update={
            "messages": [agent_message],
            "last_agent": "Researcher"
        }
    )


def analysis_agent(state: ConversationState) -> Command[Literal["router"]]:
    """
    Analysis Agent Node
    
    Specializes in analyzing, synthesizing, and providing insights.
    Works with existing conversation context and doesn't need external tools.
    """
    messages = state["messages"]
    latest_message = messages[-1]
    
    # Extract the user's request (remove @mention)
    request = re.sub(r'@\w+\s*', '', latest_message.content).strip()
    
    # Get conversation context (last 10 messages for analysis)
    context_messages = messages[-10:] if len(messages) > 10 else messages
    context_text = "\n".join([
        f"{msg.name or 'User'}: {msg.content}" 
        for msg in context_messages[:-1]  # Exclude the current message
    ])
    
    system_prompt = SystemMessage(content="""
    You are an Analysis Agent specializing in synthesizing information and providing insights.
    
    Your role is to:
    1. Analyze patterns and trends in the provided information
    2. Draw connections between different data points
    3. Provide strategic insights and recommendations
    4. Identify implications and potential outcomes
    5. Suggest actionable next steps
    
    Be analytical, insightful, and focus on the bigger picture while remaining grounded in the data.
    """)
    
    human_prompt = HumanMessage(content=f"""
    Analysis Request: {request}
    
    Conversation Context:
    {context_text}
    
    Please provide a thorough analysis with insights and recommendations.
    """)
    
    try:
        response = azure_llm.invoke([system_prompt, human_prompt])
        
        agent_message = AIMessage(
            content=response.content,
            name="Analyst"
        )
        
    except Exception as e:
        agent_message = AIMessage(
            content=f"I apologize, but I encountered an issue while analyzing your request: {str(e)}. Please try again.",
            name="Analyst"
        )
    
    return Command(
        goto="router",
        update={
            "messages": [agent_message],
            "last_agent": "Analyst"
        }
    )


def summarizer_agent(state: ConversationState) -> Command[Literal["router"]]:
    """
    Summarizer Agent Node
    
    Specializes in creating concise summaries and generating relevant tags.
    Useful for thread management and organization.
    """
    messages = state["messages"]
    
    # Get all messages for summarization
    conversation_text = "\n".join([
        f"{msg.name or 'User'}: {msg.content}" 
        for msg in messages[:-1]  # Exclude the current @Summarizer request
    ])
    
    system_prompt = SystemMessage(content="""
    You are a Summarizer Agent specializing in creating concise summaries and organizing information.
    
    Your role is to:
    1. Create clear, concise summaries of conversations
    2. Identify key topics and themes
    3. Generate relevant tags for categorization
    4. Highlight important decisions or conclusions
    5. Note any action items or follow-ups
    
    Format your response with:
    - **Summary**: A brief overview of the conversation
    - **Key Topics**: Main themes discussed
    - **Tags**: Relevant tags for categorization (comma-separated)
    - **Action Items**: Any next steps or follow-ups identified
    """)
    
    human_prompt = HumanMessage(content=f"""
    Please summarize the following conversation:
    
    {conversation_text}
    """)
    
    try:
        response = azure_llm.invoke([system_prompt, human_prompt])
        
        agent_message = AIMessage(
            content=response.content,
            name="Summarizer"
        )
        
    except Exception as e:
        agent_message = AIMessage(
            content=f"I apologize, but I encountered an issue while summarizing: {str(e)}. Please try again.",
            name="Summarizer"
        )
    
    return Command(
        goto="router",
        update={
            "messages": [agent_message],
            "last_agent": "Summarizer"
        }
    )


# ============================================================================
# ROUTER LOGIC
# ============================================================================

def parse_mention(message_content: str) -> Optional[str]:
    """
    Parse @mentions from message content.

    Returns the mentioned agent name (without @) or None if no mention found.
    Supported agents: @Researcher, @Analyst, @Summarizer
    """
    mention_pattern = r'@(Researcher|Analyst|Summarizer)\b'
    match = re.search(mention_pattern, message_content, re.IGNORECASE)

    if match:
        return match.group(1).lower()
    return None


def router_node(state: ConversationState) -> Command[Literal["research_agent", "analysis_agent", "summarizer_agent", "__end__"]]:
    """
    Router Node - Central routing logic for @mention handling

    This is the core of the multi-agent system. It:
    1. Examines the latest message for @mentions
    2. Routes to the appropriate agent based on the mention
    3. Handles cases where no agent is mentioned
    4. Updates state with routing information
    """
    messages = state["messages"]

    if not messages:
        return Command(goto="__end__")

    latest_message = messages[-1]

    # Parse @mention from the latest message
    mentioned_agent = parse_mention(latest_message.content)

    # Route based on @mention
    if mentioned_agent == "researcher":
        return Command(
            goto="research_agent",
            update={"mentioned_agent": "Researcher"}
        )
    elif mentioned_agent == "analyst":
        return Command(
            goto="analysis_agent",
            update={"mentioned_agent": "Analyst"}
        )
    elif mentioned_agent == "summarizer":
        return Command(
            goto="summarizer_agent",
            update={"mentioned_agent": "Summarizer"}
        )
    else:
        # No @mention found - provide guidance or route to default agent
        guidance_message = AIMessage(
            content="""I'm ready to help! Please mention one of our specialized agents:

• **@Researcher** - For finding information, data, and research
• **@Analyst** - For analysis, insights, and strategic thinking
• **@Summarizer** - For summaries, organization, and key takeaways

Example: "@Researcher What's the latest on renewable energy trends?"
""",
            name="System"
        )

        return Command(
            goto="__end__",
            update={
                "messages": [guidance_message],
                "mentioned_agent": None
            }
        )


# ============================================================================
# GRAPH CONSTRUCTION
# ============================================================================

def create_conversation_graph() -> StateGraph:
    """
    Create and compile the conversation graph.

    Graph Structure:
    START -> router -> [research_agent|analysis_agent|summarizer_agent] -> router -> END

    The router acts as a central hub that:
    1. Receives all user messages
    2. Parses @mentions to determine routing
    3. Directs flow to appropriate agent nodes
    4. Handles responses and determines if conversation should continue or end
    """

    # Initialize the StateGraph with our conversation state
    builder = StateGraph(ConversationState)

    # Add all nodes to the graph
    builder.add_node("router", router_node)
    builder.add_node("research_agent", research_agent)
    builder.add_node("analysis_agent", analysis_agent)
    builder.add_node("summarizer_agent", summarizer_agent)

    # Define the graph flow
    # All conversations start at the router
    builder.add_edge(START, "router")

    # All agents return to router after processing
    # The router then decides whether to end or continue
    builder.add_edge("research_agent", "router")
    builder.add_edge("analysis_agent", "router")
    builder.add_edge("summarizer_agent", "router")

    # Compile the graph
    graph = builder.compile()

    return graph


# ============================================================================
# GRAPH INSTANCE
# ============================================================================

# Create the global graph instance
conversation_graph = create_conversation_graph()
