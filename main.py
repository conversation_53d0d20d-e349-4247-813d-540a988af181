"""
FastAPI main application for the collaborative intelligence platform.

This is the entry point for the multi-agent AI conversation system.
It sets up the FastAPI app, database, and routing.
"""
import os
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv

from database import create_tables
from api_router import router
from file_router import router as file_router
from auth_router import router as auth_router
from websocket_router import router as websocket_router

# Load environment variables
load_dotenv()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager.
    
    Handles startup and shutdown events:
    - On startup: Create database tables
    - On shutdown: Clean up resources
    """
    # Startup
    print("🚀 Starting Collaborative Intelligence Platform...")
    
    # Validate required environment variables
    required_env_vars = [
        "AZURE_OPENAI_API_KEY",
        "AZURE_OPENAI_ENDPOINT", 
        "TAVILY_API_KEY"
    ]
    
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    if missing_vars:
        raise RuntimeError(f"Missing required environment variables: {', '.join(missing_vars)}")
    
    # Create database tables
    try:
        create_tables()
        print("✅ Database tables created successfully")

        # Create demo users
        from database import SessionLocal
        from auth_service import create_demo_users
        db = SessionLocal()
        try:
            create_demo_users(db)
        finally:
            db.close()

    except Exception as e:
        print(f"❌ Failed to create database tables: {e}")
        raise
    
    print("🎯 Multi-agent system ready!")
    print("📝 Available agents: @Researcher, @Analyst, @Summarizer, @Writer, @Coder, @Translator")
    
    yield
    
    # Shutdown
    print("🛑 Shutting down Collaborative Intelligence Platform...")


# Create FastAPI application
app = FastAPI(
    title="Collaborative Intelligence Platform",
    description="""
    A comprehensive multi-agent AI collaboration platform with advanced features for team productivity.

    ## 🤖 Multi-Agent System

    * **6 Specialized AI Agents** with @mention routing:
        * **@Researcher**: Web search and information gathering using Tavily API
        * **@Analyst**: Strategic analysis, insights, and business intelligence
        * **@Summarizer**: Conversation summaries and content organization
        * **@Writer**: Creative and professional writing tasks
        * **@Coder**: Programming, debugging, and technical solutions
        * **@Translator**: Multi-language translation and localization

    ## 🚀 Advanced Features

    * **File Upload & Processing**: Support for PDF, DOCX, TXT, MD, code files with automatic text extraction
    * **Multi-Format Export**: Export conversations as JSON, CSV, Markdown, or plain text
    * **User Authentication**: JWT-based authentication with demo users
    * **Real-Time Communication**: WebSocket support for live updates and typing indicators
    * **Persistent Storage**: Full conversation history with SQLite/PostgreSQL
    * **Modern Frontend**: React-based UI with real-time chat interface

    ## 📡 API Endpoints

    ### Core Features
    * `POST /api/threads` - Create conversation threads
    * `POST /api/threads/{id}/messages` - Send messages with @mention routing
    * `GET /api/threads/{id}/export?format=json|csv|markdown|txt` - Export conversations

    ### File Management
    * `POST /api/files/upload` - Upload and process documents
    * `GET /api/files/thread/{id}` - Get thread files
    * `GET /api/files/{id}/content` - Get extracted text content

    ### Authentication
    * `POST /api/auth/login` - User authentication (demo: username=demo, password=demo123)
    * `POST /api/auth/register` - User registration
    * `GET /api/auth/me` - Current user info

    ### Real-Time
    * `WS /ws/thread/{id}` - Thread-specific WebSocket connection
    * `WS /ws/global` - Global platform WebSocket connection

    ## 🏗️ Architecture

    * **Backend**: FastAPI + LangGraph + Azure OpenAI + Tavily Search
    * **Frontend**: React + Vite + Tailwind CSS + WebSocket
    * **Database**: SQLite (dev) / PostgreSQL (prod)
    * **Authentication**: JWT tokens with bcrypt password hashing
    * **Real-Time**: WebSocket connections with connection management
    * **File Processing**: Multi-format document parsing and text extraction
    """,
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(router)
app.include_router(file_router)
app.include_router(auth_router)
app.include_router(websocket_router)


# ============================================================================
# ROOT ENDPOINTS
# ============================================================================

@app.get("/")
async def root():
    """Root endpoint with platform information."""
    return {
        "message": "Welcome to the Collaborative Intelligence Platform",
        "version": "1.0.0",
        "agents": {
            "@Researcher": "Web search and information gathering",
            "@Analyst": "Analysis, insights, and strategic thinking",
            "@Summarizer": "Conversation summaries and organization",
            "@Writer": "Creative and professional writing tasks",
            "@Coder": "Programming, debugging, and technical solutions",
            "@Translator": "Language translation and localization"
        },
        "endpoints": {
            "create_thread": "POST /api/threads",
            "send_message": "POST /api/threads/{thread_id}/messages",
            "list_threads": "GET /api/threads",
            "get_thread": "GET /api/threads/{thread_id}"
        },
        "docs": "/docs"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "agents_available": ["Researcher", "Analyst", "Summarizer", "Writer", "Coder", "Translator"],
        "database": "connected"
    }


# ============================================================================
# ERROR HANDLERS
# ============================================================================

@app.exception_handler(404)
async def not_found_handler(request, exc):
    """Custom 404 handler."""
    return {
        "error": "Not Found",
        "message": "The requested resource was not found",
        "suggestion": "Check the API documentation at /docs"
    }


@app.exception_handler(500)
async def internal_error_handler(request, exc):
    """Custom 500 handler."""
    return {
        "error": "Internal Server Error",
        "message": "An unexpected error occurred",
        "suggestion": "Please try again or contact support"
    }


if __name__ == "__main__":
    import uvicorn
    
    # Run the application
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
