"""
FastAPI main application for the collaborative intelligence platform.

This is the entry point for the multi-agent AI conversation system.
It sets up the FastAPI app, database, and routing.
"""
import os
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv

from database import create_tables
from api_router import router

# Load environment variables
load_dotenv()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager.
    
    Handles startup and shutdown events:
    - On startup: Create database tables
    - On shutdown: Clean up resources
    """
    # Startup
    print("🚀 Starting Collaborative Intelligence Platform...")
    
    # Validate required environment variables
    required_env_vars = [
        "AZURE_OPENAI_API_KEY",
        "AZURE_OPENAI_ENDPOINT", 
        "TAVILY_API_KEY"
    ]
    
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    if missing_vars:
        raise RuntimeError(f"Missing required environment variables: {', '.join(missing_vars)}")
    
    # Create database tables
    try:
        create_tables()
        print("✅ Database tables created successfully")
    except Exception as e:
        print(f"❌ Failed to create database tables: {e}")
        raise
    
    print("🎯 Multi-agent system ready!")
    print("📝 Available agents: @Researcher, @Analyst, @Summarizer")
    
    yield
    
    # Shutdown
    print("🛑 Shutting down Collaborative Intelligence Platform...")


# Create FastAPI application
app = FastAPI(
    title="Collaborative Intelligence Platform",
    description="""
    A multi-agent AI conversation platform that supports dynamic routing to specialized agents.
    
    ## Features
    
    * **Multi-Agent System**: Route conversations to specialized AI agents using @mentions
    * **Persistent Conversations**: Threads maintain full conversation history
    * **Specialized Agents**:
        * **@Researcher**: Web search and information gathering using Tavily
        * **@Analyst**: Analysis, insights, and strategic thinking
        * **@Summarizer**: Conversation summaries and organization
    
    ## Usage
    
    1. Create a new thread: `POST /api/threads`
    2. Send messages with @mentions: `POST /api/threads/{thread_id}/messages`
    
    Example message: "@Researcher What are the latest trends in renewable energy?"
    
    ## Architecture
    
    Built with:
    * **FastAPI** for the REST API
    * **LangGraph** for multi-agent orchestration
    * **Azure OpenAI** for language model capabilities
    * **PostgreSQL** for persistent storage
    * **Tavily** for web search capabilities
    """,
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API router
app.include_router(router)


# ============================================================================
# ROOT ENDPOINTS
# ============================================================================

@app.get("/")
async def root():
    """Root endpoint with platform information."""
    return {
        "message": "Welcome to the Collaborative Intelligence Platform",
        "version": "1.0.0",
        "agents": {
            "@Researcher": "Web search and information gathering",
            "@Analyst": "Analysis, insights, and strategic thinking", 
            "@Summarizer": "Conversation summaries and organization"
        },
        "endpoints": {
            "create_thread": "POST /api/threads",
            "send_message": "POST /api/threads/{thread_id}/messages",
            "list_threads": "GET /api/threads",
            "get_thread": "GET /api/threads/{thread_id}"
        },
        "docs": "/docs"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "agents_available": ["Researcher", "Analyst", "Summarizer"],
        "database": "connected"
    }


# ============================================================================
# ERROR HANDLERS
# ============================================================================

@app.exception_handler(404)
async def not_found_handler(request, exc):
    """Custom 404 handler."""
    return {
        "error": "Not Found",
        "message": "The requested resource was not found",
        "suggestion": "Check the API documentation at /docs"
    }


@app.exception_handler(500)
async def internal_error_handler(request, exc):
    """Custom 500 handler."""
    return {
        "error": "Internal Server Error", 
        "message": "An unexpected error occurred",
        "suggestion": "Please try again or contact support"
    }


if __name__ == "__main__":
    import uvicorn
    
    # Run the application
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
