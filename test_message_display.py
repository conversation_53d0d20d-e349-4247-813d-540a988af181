"""
Test script to verify that user messages are properly displayed in the chat.
"""
import requests
import time
import json

BASE_URL = "http://localhost:8000"

def test_message_display():
    """Test that user messages appear in the chat window."""
    print("🧪 Testing Message Display")
    print("=" * 40)
    
    # First, create a test thread
    print("📝 Creating test thread...")
    thread_data = {
        "title": "Message Display Test",
        "description": "Testing that user messages appear in chat"
    }
    
    response = requests.post(f"{BASE_URL}/api/threads", json=thread_data)
    
    if response.status_code != 201:
        print(f"❌ Failed to create thread: {response.status_code}")
        return False
    
    thread = response.json()
    thread_id = thread["id"]
    print(f"✅ Created thread with ID: {thread_id}")
    
    # Send a test message
    print("\n💬 Sending test message...")
    message_data = {
        "content": "Hello! This is a test message that should appear in the chat.",
        "author": "TestUser"
    }
    
    response = requests.post(f"{BASE_URL}/api/threads/{thread_id}/messages", json=message_data)
    
    if response.status_code != 200:
        print(f"❌ Failed to send message: {response.status_code}")
        if response.content:
            print(f"   Error details: {response.text}")
        return False
    
    result = response.json()
    print(f"✅ Message sent successfully")
    print(f"   API Response: {result.get('content', 'No content')[:50]}...")
    
    # Wait a moment for processing
    time.sleep(2)
    
    # Fetch the thread to verify messages are stored
    print("\n🔍 Fetching thread to verify messages...")
    response = requests.get(f"{BASE_URL}/api/threads/{thread_id}")
    
    if response.status_code != 200:
        print(f"❌ Failed to fetch thread: {response.status_code}")
        return False
    
    thread_data = response.json()
    messages = thread_data.get("messages", [])
    
    print(f"📊 Thread Analysis:")
    print(f"   Total messages: {len(messages)}")
    
    if len(messages) == 0:
        print("❌ No messages found in thread!")
        return False
    
    # Check if user message is present
    user_messages = [msg for msg in messages if msg.get("author") == "TestUser"]
    agent_messages = [msg for msg in messages if msg.get("author_type") == "agent"]
    
    print(f"   User messages: {len(user_messages)}")
    print(f"   Agent messages: {len(agent_messages)}")
    
    if len(user_messages) == 0:
        print("❌ User message not found in thread!")
        print("   This means user messages are not being stored properly")
        return False
    
    print("\n📜 Messages in thread:")
    for i, msg in enumerate(messages, 1):
        author_type = msg.get("author_type", "unknown")
        author = msg.get("author", "Unknown")
        content_preview = msg.get("content", "")[:60] + "..." if len(msg.get("content", "")) > 60 else msg.get("content", "")
        
        print(f"   {i}. [{author_type}] {author}: {content_preview}")
    
    # Verify the user message content
    user_message = user_messages[0]
    if user_message.get("content") == message_data["content"]:
        print("✅ User message content matches what was sent")
    else:
        print("❌ User message content doesn't match")
        print(f"   Expected: {message_data['content']}")
        print(f"   Found: {user_message.get('content')}")
        return False
    
    print("\n🎉 Message display test PASSED!")
    print(f"\n🌐 Verify in frontend:")
    print(f"   1. Go to http://localhost:3000/thread/{thread_id}")
    print(f"   2. You should see the user message: '{message_data['content']}'")
    print(f"   3. Try sending a new message - it should appear immediately")
    
    return True

def test_real_time_message_sending():
    """Test sending multiple messages to verify real-time behavior."""
    print("\n🧪 Testing Real-Time Message Sending")
    print("=" * 45)
    
    # Get existing threads
    response = requests.get(f"{BASE_URL}/api/threads")
    if response.status_code != 200:
        print("❌ Failed to fetch threads")
        return False
    
    threads = response.json()
    if len(threads) == 0:
        print("❌ No threads available for testing")
        return False
    
    # Use the first thread
    thread = threads[0]
    thread_id = thread["id"]
    print(f"📝 Using thread: '{thread['title']}' (ID: {thread_id})")
    
    # Send multiple test messages
    test_messages = [
        "First test message - should appear immediately",
        "Second test message - testing rapid sending",
        "@Researcher Can you help me with some research?",
        "Final test message - checking message order"
    ]
    
    print(f"\n💬 Sending {len(test_messages)} test messages...")
    
    for i, content in enumerate(test_messages, 1):
        print(f"   {i}. Sending: {content[:40]}...")
        
        message_data = {
            "content": content,
            "author": "TestUser"
        }
        
        response = requests.post(f"{BASE_URL}/api/threads/{thread_id}/messages", json=message_data)
        
        if response.status_code == 200:
            print(f"      ✅ Sent successfully")
        else:
            print(f"      ❌ Failed: {response.status_code}")
        
        # Small delay between messages
        time.sleep(1)
    
    print(f"\n🔍 Verifying messages were stored...")
    
    # Fetch thread to verify all messages
    response = requests.get(f"{BASE_URL}/api/threads/{thread_id}")
    if response.status_code != 200:
        print("❌ Failed to fetch thread")
        return False
    
    thread_data = response.json()
    messages = thread_data.get("messages", [])
    
    # Count recent test messages
    recent_test_messages = [
        msg for msg in messages 
        if msg.get("author") == "TestUser" and 
        any(test_content in msg.get("content", "") for test_content in test_messages)
    ]
    
    print(f"📊 Results:")
    print(f"   Total messages in thread: {len(messages)}")
    print(f"   Recent test messages found: {len(recent_test_messages)}")
    print(f"   Expected test messages: {len(test_messages)}")
    
    if len(recent_test_messages) >= len(test_messages):
        print("✅ All test messages were stored successfully!")
        return True
    else:
        print("❌ Some test messages were not stored")
        return False

def main():
    """Run message display tests."""
    print("🧪 Message Display Test Suite")
    print("=" * 50)
    
    try:
        # Test basic message display
        basic_test_success = test_message_display()
        
        # Test real-time message sending
        realtime_test_success = test_real_time_message_sending()
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 Test Results Summary:")
        print(f"   Basic message display: {'✅ PASS' if basic_test_success else '❌ FAIL'}")
        print(f"   Real-time messaging: {'✅ PASS' if realtime_test_success else '❌ FAIL'}")
        
        overall_success = basic_test_success and realtime_test_success
        
        if overall_success:
            print("\n🎉 All message display tests PASSED!")
            print("\n💡 Frontend Fix Applied:")
            print("   • User messages now appear immediately in chat")
            print("   • Fixed Vite HMR warning for better development")
            print("   • Messages are properly stored and displayed")
        else:
            print("\n❌ Some message display tests FAILED!")
            print("   Check the frontend and backend message handling")
        
        return overall_success
        
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Make sure the server is running at http://localhost:8000")
        return False
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
