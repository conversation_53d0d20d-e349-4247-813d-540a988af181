"""
Simple test script for the Collaborative Intelligence Platform API.

This script demonstrates how to interact with the multi-agent system.
"""
import requests
import json
import time
from typing import Dict, Any

BASE_URL = "http://localhost:8000"


def test_health_check():
    """Test the health check endpoint."""
    print("🔍 Testing health check...")
    response = requests.get(f"{BASE_URL}/health")
    
    if response.status_code == 200:
        print("✅ Health check passed")
        print(f"   Response: {response.json()}")
    else:
        print(f"❌ Health check failed: {response.status_code}")
    
    return response.status_code == 200


def create_test_thread() -> int:
    """Create a test thread and return its ID."""
    print("\n📝 Creating test thread...")
    
    thread_data = {
        "title": "Test Multi-Agent Conversation",
        "description": "Testing the @mention routing system"
    }
    
    response = requests.post(f"{BASE_URL}/api/threads", json=thread_data)
    
    if response.status_code == 201:
        thread = response.json()
        thread_id = thread["id"]
        print(f"✅ Thread created successfully (ID: {thread_id})")
        print(f"   Title: {thread['title']}")
        return thread_id
    else:
        print(f"❌ Failed to create thread: {response.status_code}")
        print(f"   Error: {response.text}")
        return None


def send_test_message(thread_id: int, content: str, author: str = "TestUser") -> Dict[Any, Any]:
    """Send a test message to the thread."""
    print(f"\n💬 Sending message: '{content}'")
    
    message_data = {
        "content": content,
        "author": author
    }
    
    response = requests.post(f"{BASE_URL}/api/threads/{thread_id}/messages", json=message_data)
    
    if response.status_code == 200:
        agent_response = response.json()
        print(f"✅ Message sent successfully")
        print(f"   Agent: {agent_response['author']}")
        print(f"   Response: {agent_response['content'][:200]}...")
        return agent_response
    else:
        print(f"❌ Failed to send message: {response.status_code}")
        print(f"   Error: {response.text}")
        return None


def get_thread_history(thread_id: int):
    """Get the full thread history."""
    print(f"\n📖 Getting thread history...")
    
    response = requests.get(f"{BASE_URL}/api/threads/{thread_id}")
    
    if response.status_code == 200:
        thread = response.json()
        print(f"✅ Thread retrieved successfully")
        print(f"   Title: {thread['title']}")
        print(f"   Messages: {len(thread['messages'])}")
        
        for i, msg in enumerate(thread['messages'], 1):
            print(f"   {i}. {msg['author']}: {msg['content'][:100]}...")
        
        return thread
    else:
        print(f"❌ Failed to get thread: {response.status_code}")
        print(f"   Error: {response.text}")
        return None


def run_comprehensive_test():
    """Run a comprehensive test of the multi-agent system."""
    print("🚀 Starting Collaborative Intelligence Platform Test")
    print("=" * 60)
    
    # Test health check
    if not test_health_check():
        print("❌ Health check failed, aborting tests")
        return
    
    # Create test thread
    thread_id = create_test_thread()
    if not thread_id:
        print("❌ Thread creation failed, aborting tests")
        return
    
    # Test different agent interactions
    test_messages = [
        "@Researcher What are the latest developments in artificial intelligence?",
        "@Analyst What are the implications of AI advancement for businesses?",
        "@Summarizer Please summarize our discussion so far",
        "Hello without any mention"  # Test guidance message
    ]
    
    for message in test_messages:
        send_test_message(thread_id, message)
        time.sleep(2)  # Brief pause between requests
    
    # Get final thread history
    get_thread_history(thread_id)
    
    print("\n" + "=" * 60)
    print("🎉 Test completed! Check the responses above.")
    print(f"📊 Thread ID {thread_id} contains the full conversation history.")


if __name__ == "__main__":
    try:
        run_comprehensive_test()
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Make sure the server is running at http://localhost:8000")
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
