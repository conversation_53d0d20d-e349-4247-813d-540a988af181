import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { MessageSquare, Clock, Tag, Users } from 'lucide-react'
import { generateThreadSummary, getConversationStats } from '../services/summaryService'

function ThreadList({ threads }) {
  const formatDate = (dateString) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = (now - date) / (1000 * 60 * 60)

    if (diffInHours < 1) {
      return 'Just now'
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`
    } else if (diffInHours < 168) {
      return `${Math.floor(diffInHours / 24)}d ago`
    } else {
      return date.toLocaleDateString()
    }
  }

  const getAgentBadgeClass = (agentName) => {
    const agentClasses = {
      'Researcher': 'agent-researcher',
      'Analyst': 'agent-analyst',
      'Summarizer': 'agent-summarizer',
      'Writer': 'agent-writer',
      'Coder': 'agent-coder',
      'Translator': 'agent-translator',
      'System': 'agent-system'
    }
    return `agent-badge ${agentClasses[agentName] || 'agent-system'}`
  }

  const getLastAgents = (thread) => {
    if (!thread.messages || thread.messages.length === 0) return []

    // Get unique agents from recent messages
    const recentMessages = thread.messages.slice(-5)
    const agents = [...new Set(recentMessages.map(msg => msg.author))]
    return agents.filter(agent => agent !== 'TestUser' && agent !== 'User')
  }



  return (
    <div className="space-y-4">
      {threads.map((thread) => {
        const lastAgents = getLastAgents(thread)
        const lastMessage = thread.messages?.[thread.messages.length - 1]
        const threadSummary = generateThreadSummary(thread)
        const conversationStats = getConversationStats(thread)

        return (
          <Link
            key={thread.id}
            to={`/thread/${thread.id}`}
            className="card p-6 hover:shadow-md transition-shadow duration-200 block"
          >
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-3 mb-2">
                  <h3 className="text-lg font-semibold text-gray-900 truncate">
                    {thread.title}
                  </h3>
                  <div className="flex items-center space-x-2">
                    <MessageSquare className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-gray-500">
                      {thread.message_count || thread.messages?.length || 0}
                    </span>
                  </div>
                </div>

                {/* AI-Generated Summary */}
                <div className="mb-3 p-3 bg-blue-50 border-l-4 border-blue-200 rounded-r-lg">
                  <p className="text-sm text-blue-800 font-medium line-clamp-2">
                    💡 {threadSummary}
                  </p>
                  {conversationStats.topics.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {conversationStats.topics.slice(0, 2).map((topic, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-700"
                        >
                          {topic.topic}
                        </span>
                      ))}
                    </div>
                  )}
                </div>

                {thread.description && (
                  <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                    {thread.description}
                  </p>
                )}

                {lastMessage && (
                  <div className="mb-3">
                    <p className="text-sm text-gray-500 line-clamp-2">
                      <span className="font-medium">{lastMessage.author}:</span>{' '}
                      {lastMessage.content}
                    </p>
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    {/* Recent Agents */}
                    {lastAgents.length > 0 && (
                      <div className="flex items-center space-x-1">
                        <Users className="w-3 h-3 text-gray-400" />
                        <div className="flex space-x-1">
                          {lastAgents.slice(0, 3).map((agent, index) => (
                            <span
                              key={index}
                              className={getAgentBadgeClass(agent)}
                            >
                              {agent}
                            </span>
                          ))}
                          {lastAgents.length > 3 && (
                            <span className="agent-badge agent-system">
                              +{lastAgents.length - 3}
                            </span>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Tags */}
                    {thread.tags && thread.tags.length > 0 && (
                      <div className="flex items-center space-x-1">
                        <Tag className="w-3 h-3 text-gray-400" />
                        <div className="flex space-x-1">
                          {thread.tags.slice(0, 2).map((tag) => (
                            <span
                              key={tag.id}
                              className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800"
                            >
                              {tag.name}
                            </span>
                          ))}
                          {thread.tags.length > 2 && (
                            <span className="text-xs text-gray-500">
                              +{thread.tags.length - 2}
                            </span>
                          )}
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <Clock className="w-3 h-3" />
                    <span>{formatDate(thread.updated_at)}</span>
                  </div>
                </div>
              </div>
            </div>
          </Link>
        )
      })}
    </div>
  )
}

export default ThreadList
