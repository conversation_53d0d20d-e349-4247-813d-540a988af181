import React, { createContext, useContext, useReducer, useEffect } from 'react'
import { getThreads, checkHealth } from '../services/api'

const ThreadContext = createContext()

const initialState = {
  threads: [],
  currentThread: null,
  loading: false,
  error: null,
  healthStatus: null,
  agents: []
}

function threadReducer(state, action) {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload }
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false }
    
    case 'SET_THREADS':
      return { ...state, threads: action.payload, loading: false }
    
    case 'ADD_THREAD':
      return { ...state, threads: [action.payload, ...state.threads] }
    
    case 'SET_CURRENT_THREAD':
      return { ...state, currentThread: action.payload }
    
    case 'UPDATE_THREAD':
      return {
        ...state,
        threads: state.threads.map(thread =>
          thread.id === action.payload.id ? action.payload : thread
        ),
        currentThread: state.currentThread?.id === action.payload.id 
          ? action.payload 
          : state.currentThread
      }
    
    case 'ADD_MESSAGE_TO_THREAD':
      const { threadId, message } = action.payload
      const updatedThreads = state.threads.map(thread =>
        thread.id === threadId
          ? {
              ...thread,
              messages: [...(thread.messages || []), message],
              updated_at: new Date().toISOString() // Update timestamp when message is added
            }
          : thread
      )

      // Re-sort threads to maintain newest-first order
      const sortedThreads = updatedThreads.sort((a, b) => {
        const aTime = new Date(a.updated_at || a.created_at)
        const bTime = new Date(b.updated_at || b.created_at)
        return bTime - aTime
      })

      return {
        ...state,
        threads: sortedThreads,
        currentThread: state.currentThread?.id === threadId
          ? {
              ...state.currentThread,
              messages: [...(state.currentThread.messages || []), message],
              updated_at: new Date().toISOString()
            }
          : state.currentThread
      }
    
    case 'SET_HEALTH_STATUS':
      return { ...state, healthStatus: action.payload }
    
    case 'CLEAR_ERROR':
      return { ...state, error: null }
    
    default:
      return state
  }
}

export function ThreadProvider({ children }) {
  const [state, dispatch] = useReducer(threadReducer, initialState)

  // Load threads on mount
  useEffect(() => {
    loadThreads()
    checkSystemHealth()
  }, [])

  const loadThreads = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true })
      const threads = await getThreads()

      // Sort threads by most recent first (updated_at, then created_at)
      const sortedThreads = threads.sort((a, b) => {
        const aTime = new Date(a.updated_at || a.created_at)
        const bTime = new Date(b.updated_at || b.created_at)
        return bTime - aTime // Descending order (newest first)
      })

      dispatch({ type: 'SET_THREADS', payload: sortedThreads })
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to load threads' })
      console.error('Error loading threads:', error)
    }
  }

  const checkSystemHealth = async () => {
    try {
      const health = await checkHealth()
      dispatch({ type: 'SET_HEALTH_STATUS', payload: health })
    } catch (error) {
      console.error('Health check failed:', error)
      dispatch({ type: 'SET_HEALTH_STATUS', payload: { status: 'unhealthy' } })
    }
  }

  const addThread = (thread) => {
    dispatch({ type: 'ADD_THREAD', payload: thread })
  }

  const setCurrentThread = (thread) => {
    dispatch({ type: 'SET_CURRENT_THREAD', payload: thread })
  }

  const updateThread = (thread) => {
    dispatch({ type: 'UPDATE_THREAD', payload: thread })
  }

  const addMessageToThread = (threadId, message) => {
    dispatch({ type: 'ADD_MESSAGE_TO_THREAD', payload: { threadId, message } })
  }

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' })
  }

  const value = {
    ...state,
    loadThreads,
    addThread,
    setCurrentThread,
    updateThread,
    addMessageToThread,
    clearError,
    checkSystemHealth
  }

  return (
    <ThreadContext.Provider value={value}>
      {children}
    </ThreadContext.Provider>
  )
}

export const useThread = () => {
  const context = useContext(ThreadContext)
  if (!context) {
    throw new Error('useThread must be used within a ThreadProvider')
  }
  return context
}
