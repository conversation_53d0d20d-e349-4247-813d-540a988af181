"""
Test script for enhanced backend features.

Tests:
1. File upload and processing
2. Conversation export in multiple formats
3. User authentication
4. WebSocket real-time communication
"""
import requests
import json
import time
import asyncio
import websockets
from pathlib import Path

BASE_URL = "http://localhost:8000"
WS_URL = "ws://localhost:8000"


def test_authentication():
    """Test user authentication system."""
    print("🔐 Testing Authentication System")
    print("-" * 40)
    
    # Setup demo users
    response = requests.post(f"{BASE_URL}/api/auth/demo-setup")
    if response.status_code == 200:
        print("✅ Demo users created")
        demo_info = response.json()
        print(f"   Available users: {[u['username'] for u in demo_info['users']]}")
    
    # Test login
    login_data = {"username": "demo", "password": "demo123"}
    response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
    
    if response.status_code == 200:
        token_info = response.json()
        print("✅ Login successful")
        print(f"   User: {token_info['user']['username']}")
        print(f"   Token expires in: {token_info['expires_in']} seconds")
        return token_info["access_token"]
    else:
        print(f"❌ Login failed: {response.status_code}")
        return None


def test_file_upload(token=None):
    """Test file upload functionality."""
    print("\n📁 Testing File Upload")
    print("-" * 40)
    
    # Create a test file
    test_content = """# Test Document

This is a test document for the collaborative intelligence platform.

## Features
- Multi-agent conversations
- File upload and processing
- Real-time communication
- Export capabilities

## Agents
1. @Researcher - Information gathering
2. @Analyst - Strategic analysis
3. @Writer - Content creation
4. @Coder - Programming solutions
5. @Translator - Language translation
6. @Summarizer - Content organization
"""
    
    test_file_path = Path("test_document.md")
    test_file_path.write_text(test_content)
    
    try:
        # Upload file
        headers = {}
        if token:
            headers["Authorization"] = f"Bearer {token}"
        
        with open(test_file_path, 'rb') as f:
            files = {"file": ("test_document.md", f, "text/markdown")}
            response = requests.post(f"{BASE_URL}/api/files/upload", files=files, headers=headers)
        
        if response.status_code == 201:
            file_info = response.json()
            print("✅ File uploaded successfully")
            print(f"   File ID: {file_info['id']}")
            print(f"   Original name: {file_info['filename']}")
            print(f"   Type: {file_info['file_type']}")
            print(f"   Size: {file_info['file_size']} bytes")
            print(f"   Text extracted: {file_info['text_length']} characters")
            return file_info["id"]
        else:
            print(f"❌ File upload failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return None
            
    finally:
        # Clean up test file
        if test_file_path.exists():
            test_file_path.unlink()


def test_conversation_export():
    """Test conversation export functionality."""
    print("\n📤 Testing Conversation Export")
    print("-" * 40)
    
    # Create a test thread first
    thread_data = {
        "title": "Export Test Conversation",
        "description": "Testing export functionality with multiple agents"
    }
    
    response = requests.post(f"{BASE_URL}/api/threads", json=thread_data)
    if response.status_code != 201:
        print(f"❌ Failed to create test thread: {response.status_code}")
        return
    
    thread_id = response.json()["id"]
    print(f"✅ Created test thread (ID: {thread_id})")
    
    # Add some test messages
    test_messages = [
        "@Researcher What are the benefits of renewable energy?",
        "@Analyst What are the market opportunities in renewable energy?",
        "@Summarizer Please summarize our discussion"
    ]
    
    for message in test_messages:
        message_data = {"content": message, "author": "TestUser"}
        response = requests.post(f"{BASE_URL}/api/threads/{thread_id}/messages", json=message_data)
        if response.status_code == 200:
            print(f"✅ Added message: {message[:50]}...")
        time.sleep(1)  # Brief pause between messages
    
    # Test different export formats
    formats = ["json", "csv", "markdown", "txt"]
    
    for format_type in formats:
        response = requests.get(f"{BASE_URL}/api/threads/{thread_id}/export?format={format_type}")
        
        if response.status_code == 200:
            # Save exported file
            filename = f"exported_conversation.{format_type}"
            with open(filename, 'wb') as f:
                f.write(response.content)
            
            file_size = Path(filename).stat().st_size
            print(f"✅ Exported as {format_type.upper()}: {filename} ({file_size} bytes)")
            
            # Clean up
            Path(filename).unlink()
        else:
            print(f"❌ Export failed for {format_type}: {response.status_code}")


async def test_websocket_connection():
    """Test WebSocket real-time communication."""
    print("\n🔌 Testing WebSocket Connection")
    print("-" * 40)
    
    try:
        # Connect to global WebSocket
        uri = f"{WS_URL}/ws/global?user_id=test_user"
        
        async with websockets.connect(uri) as websocket:
            print("✅ Connected to global WebSocket")
            
            # Send ping
            ping_message = {
                "type": "ping",
                "data": {"timestamp": time.time()}
            }
            await websocket.send(json.dumps(ping_message))
            
            # Wait for responses
            for _ in range(3):
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                    message = json.loads(response)
                    print(f"✅ Received: {message['type']}")
                    
                    if message["type"] == "system":
                        print(f"   Welcome message: {message['data']['message']}")
                    elif message["type"] == "pong":
                        print(f"   Pong response received")
                        
                except asyncio.TimeoutError:
                    break
            
            # Test stats request
            stats_message = {
                "type": "get_stats",
                "data": {}
            }
            await websocket.send(json.dumps(stats_message))
            
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                message = json.loads(response)
                if message["type"] == "stats":
                    stats = message["data"]
                    print(f"✅ Platform stats:")
                    print(f"   Total connections: {stats['total_connections']}")
                    print(f"   Active threads: {stats['active_threads']}")
                    print(f"   Agents available: {stats['agents_available']}")
            except asyncio.TimeoutError:
                print("⚠️  No stats response received")
                
    except Exception as e:
        print(f"❌ WebSocket test failed: {e}")


def test_enhanced_api_features():
    """Test enhanced API features."""
    print("\n🚀 Testing Enhanced API Features")
    print("-" * 40)
    
    # Test health endpoint with new features
    response = requests.get(f"{BASE_URL}/health")
    if response.status_code == 200:
        health = response.json()
        print("✅ Health check passed")
        print(f"   Status: {health['status']}")
        print(f"   Agents: {', '.join(health['agents_available'])}")
        print(f"   Database: {health['database']}")
    
    # Test file listing
    response = requests.get(f"{BASE_URL}/api/files/")
    if response.status_code == 200:
        files = response.json()
        print(f"✅ File listing: {len(files)} files found")
    
    # Test user listing
    response = requests.get(f"{BASE_URL}/api/auth/users")
    if response.status_code == 200:
        users = response.json()
        print(f"✅ User listing: {len(users)} users found")
        for user in users[:3]:  # Show first 3 users
            print(f"   - {user['username']} ({user['email'] or 'no email'})")


def run_comprehensive_test():
    """Run comprehensive test of all enhanced features."""
    print("🧪 Comprehensive Enhanced Features Test")
    print("=" * 60)
    
    # Test authentication
    token = test_authentication()
    
    # Test file upload
    file_id = test_file_upload(token)
    
    # Test conversation export
    test_conversation_export()
    
    # Test WebSocket (async)
    asyncio.run(test_websocket_connection())
    
    # Test enhanced API features
    test_enhanced_api_features()
    
    print("\n" + "=" * 60)
    print("🎉 Enhanced features test completed!")
    print("\nNew capabilities available:")
    print("✅ File upload and text extraction")
    print("✅ Multi-format conversation export")
    print("✅ User authentication with JWT tokens")
    print("✅ Real-time WebSocket communication")
    print("✅ Enhanced API endpoints")


if __name__ == "__main__":
    try:
        run_comprehensive_test()
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Make sure the server is running at http://localhost:8000")
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
