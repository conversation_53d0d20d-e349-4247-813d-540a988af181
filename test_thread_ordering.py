"""
Test script to verify thread ordering - newest threads should appear first.
"""
import requests
import time
import json

BASE_URL = "http://localhost:8000"

def test_thread_ordering():
    """Test that threads are ordered with newest first."""
    print("🧪 Testing Thread Ordering")
    print("=" * 40)
    
    # Create multiple test threads with delays to ensure different timestamps
    test_threads = [
        {"title": "First Thread", "description": "Created first"},
        {"title": "Second Thread", "description": "Created second"},
        {"title": "Third Thread", "description": "Created third"},
        {"title": "Fourth Thread", "description": "Created fourth (newest)"}
    ]
    
    created_thread_ids = []
    
    print("📝 Creating test threads...")
    for i, thread_data in enumerate(test_threads, 1):
        print(f"   {i}. Creating: {thread_data['title']}")
        
        response = requests.post(f"{BASE_URL}/api/threads", json=thread_data)
        
        if response.status_code == 201:
            thread = response.json()
            created_thread_ids.append(thread["id"])
            print(f"      ✅ Created with ID: {thread['id']}")
            
            # Add a small delay to ensure different timestamps
            time.sleep(1)
        else:
            print(f"      ❌ Failed to create: {response.status_code}")
            return False
    
    print(f"\n📋 Created {len(created_thread_ids)} threads")
    print(f"   Thread IDs in creation order: {created_thread_ids}")
    
    # Fetch all threads and check ordering
    print("\n🔍 Fetching threads to verify ordering...")
    response = requests.get(f"{BASE_URL}/api/threads")
    
    if response.status_code != 200:
        print(f"❌ Failed to fetch threads: {response.status_code}")
        return False
    
    threads = response.json()
    
    # Filter to only our test threads
    test_threads_fetched = [t for t in threads if t["id"] in created_thread_ids]
    
    print(f"📊 Thread Ordering Analysis:")
    print(f"   Total threads returned: {len(threads)}")
    print(f"   Our test threads found: {len(test_threads_fetched)}")
    
    if len(test_threads_fetched) != len(created_thread_ids):
        print("❌ Not all test threads were found")
        return False
    
    # Check if threads are ordered by newest first
    print(f"\n📅 Thread Order (should be newest first):")
    
    for i, thread in enumerate(test_threads_fetched, 1):
        created_time = thread.get("created_at", "Unknown")
        updated_time = thread.get("updated_at", "Unknown")
        print(f"   {i}. {thread['title']} (ID: {thread['id']})")
        print(f"      Created: {created_time}")
        print(f"      Updated: {updated_time}")
    
    # Verify ordering - newest should be first
    expected_order = list(reversed(created_thread_ids))  # Reverse because newest should be first
    actual_order = [t["id"] for t in test_threads_fetched]
    
    print(f"\n🎯 Ordering Verification:")
    print(f"   Expected order (newest first): {expected_order}")
    print(f"   Actual order:                  {actual_order}")
    
    if actual_order == expected_order:
        print("✅ Thread ordering is CORRECT - newest threads appear first!")
        return True
    else:
        print("❌ Thread ordering is INCORRECT")
        
        # Show what the correct order should be
        print("\n📋 Correct order should be:")
        for i, thread_id in enumerate(expected_order, 1):
            thread = next(t for t in test_threads_fetched if t["id"] == thread_id)
            print(f"   {i}. {thread['title']} (ID: {thread_id})")
        
        return False

def test_thread_ordering_with_messages():
    """Test that threads move to top when new messages are added."""
    print("\n🧪 Testing Thread Ordering with Messages")
    print("=" * 45)
    
    # Get current threads
    response = requests.get(f"{BASE_URL}/api/threads")
    if response.status_code != 200:
        print("❌ Failed to fetch threads")
        return False
    
    threads = response.json()
    if len(threads) < 2:
        print("❌ Need at least 2 threads for this test")
        return False
    
    # Take the second thread (not the newest) and add a message to it
    second_thread = threads[1]
    print(f"📝 Adding message to second thread: '{second_thread['title']}' (ID: {second_thread['id']})")
    
    message_data = {
        "content": "This message should move this thread to the top!",
        "author": "TestUser"
    }
    
    response = requests.post(f"{BASE_URL}/api/threads/{second_thread['id']}/messages", json=message_data)
    
    if response.status_code != 200:
        print(f"❌ Failed to add message: {response.status_code}")
        return False
    
    print("✅ Message added successfully")
    
    # Wait a moment for the update to process
    time.sleep(1)
    
    # Fetch threads again and check if the updated thread moved to top
    response = requests.get(f"{BASE_URL}/api/threads")
    if response.status_code != 200:
        print("❌ Failed to fetch updated threads")
        return False
    
    updated_threads = response.json()
    
    if len(updated_threads) > 0 and updated_threads[0]['id'] == second_thread['id']:
        print(f"✅ Thread '{second_thread['title']}' correctly moved to the top after message!")
        return True
    else:
        print(f"❌ Thread did not move to top. Current top thread: '{updated_threads[0]['title'] if updated_threads else 'None'}'")
        return False

def cleanup_test_threads():
    """Clean up test threads (optional - for development)."""
    print("\n🧹 Note: Test threads remain in database for manual verification")
    print("   You can check the frontend to see the ordering in action")

def main():
    """Run thread ordering tests."""
    print("🧪 Thread Ordering Test Suite")
    print("=" * 50)
    
    try:
        # Test basic thread ordering
        basic_ordering_success = test_thread_ordering()
        
        # Test ordering with message updates
        message_ordering_success = test_thread_ordering_with_messages()
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 Test Results Summary:")
        print(f"   Basic thread ordering: {'✅ PASS' if basic_ordering_success else '❌ FAIL'}")
        print(f"   Message update ordering: {'✅ PASS' if message_ordering_success else '❌ FAIL'}")
        
        overall_success = basic_ordering_success and message_ordering_success
        
        if overall_success:
            print("\n🎉 All thread ordering tests PASSED!")
            print("\n🌐 Verify in your frontend:")
            print("   1. Go to http://localhost:3000")
            print("   2. Check that newest threads appear at the top")
            print("   3. Create a new thread - it should appear at the top")
            print("   4. Add a message to an older thread - it should move to the top")
        else:
            print("\n❌ Some thread ordering tests FAILED!")
            print("   Check the backend implementation for proper sorting")
        
        cleanup_test_threads()
        return overall_success
        
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Make sure the server is running at http://localhost:8000")
        return False
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
