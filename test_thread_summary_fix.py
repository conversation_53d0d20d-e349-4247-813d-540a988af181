"""
Test script to verify thread summary fix - ensuring messages are included in thread list API.
"""
import requests
import json

BASE_URL = "http://localhost:8000"

def test_thread_list_api():
    """Test that the thread list API includes message data."""
    print("🔍 Testing Thread List API Response")
    print("-" * 40)
    
    response = requests.get(f"{BASE_URL}/api/threads")
    
    if response.status_code == 200:
        threads = response.json()
        print(f"✅ Retrieved {len(threads)} threads")
        
        for i, thread in enumerate(threads[:3], 1):  # Check first 3 threads
            print(f"\n{i}. Thread: {thread['title']}")
            print(f"   ID: {thread['id']}")
            print(f"   Created: {thread['created_at']}")
            
            # Check if messages are included
            if 'messages' in thread:
                messages = thread['messages']
                print(f"   ✅ Messages included: {len(messages)} messages")
                
                if len(messages) > 0:
                    print(f"   📝 Sample message: {messages[0]['content'][:50]}...")
                    print(f"   👤 Authors: <AUTHORS>
                else:
                    print(f"   📭 No messages in this thread")
            else:
                print(f"   ❌ Messages NOT included in response")
                
            # Check description
            if thread.get('description'):
                print(f"   📄 Description: {thread['description'][:50]}...")
    else:
        print(f"❌ Failed to get threads: {response.status_code}")
        print(f"   Error: {response.text}")

def test_specific_thread():
    """Test getting a specific thread to compare with list response."""
    print(f"\n🔍 Testing Specific Thread API")
    print("-" * 30)
    
    # First get thread list to find a thread ID
    response = requests.get(f"{BASE_URL}/api/threads")
    if response.status_code == 200:
        threads = response.json()
        if threads:
            thread_id = threads[0]['id']
            print(f"Testing thread ID: {thread_id}")
            
            # Get specific thread
            response = requests.get(f"{BASE_URL}/api/threads/{thread_id}")
            if response.status_code == 200:
                thread = response.json()
                print(f"✅ Individual thread retrieved")
                print(f"   Title: {thread['title']}")
                print(f"   Messages: {len(thread.get('messages', []))}")
                
                # Compare structure
                list_thread = threads[0]
                print(f"\n📊 Structure Comparison:")
                print(f"   List API messages: {len(list_thread.get('messages', []))}")
                print(f"   Individual API messages: {len(thread.get('messages', []))}")
                
                if len(list_thread.get('messages', [])) == len(thread.get('messages', [])):
                    print(f"   ✅ Message counts match!")
                else:
                    print(f"   ⚠️  Message counts differ")
            else:
                print(f"❌ Failed to get individual thread: {response.status_code}")
        else:
            print("No threads found to test")
    else:
        print(f"❌ Failed to get thread list: {response.status_code}")

def test_summary_generation():
    """Test that summaries can be generated from the API response."""
    print(f"\n🧠 Testing Summary Generation Logic")
    print("-" * 35)
    
    response = requests.get(f"{BASE_URL}/api/threads")
    if response.status_code == 200:
        threads = response.json()
        
        for thread in threads[:3]:  # Test first 3 threads
            print(f"\n📄 Thread: {thread['title']}")
            
            # Simulate frontend summary generation logic
            messages = thread.get('messages', [])
            
            if len(messages) == 0:
                if thread.get('description'):
                    summary = thread['description'][:80] + ('...' if len(thread['description']) > 80 else '')
                    print(f"   💡 Summary (from description): {summary}")
                else:
                    print(f"   💡 Summary: New conversation - no messages yet")
            else:
                # Simple keyword-based summary
                all_text = ' '.join(msg['content'].lower() for msg in messages)
                
                if 'tariff' in all_text or 'trade' in all_text:
                    summary = "Economic discussion about trade policies and tariffs"
                elif 'test' in all_text or 'export' in all_text:
                    summary = "Testing platform functionality and features"
                elif 'file' in all_text or 'upload' in all_text:
                    summary = "File upload and document analysis conversation"
                else:
                    # Use first user message
                    user_messages = [msg for msg in messages if msg['author'] in ['User', 'TestUser']]
                    if user_messages:
                        first_msg = user_messages[0]['content'].replace('@', '').strip()
                        summary = first_msg[:60] + ('...' if len(first_msg) > 60 else '')
                    else:
                        summary = "AI agent conversation"
                
                print(f"   💡 Generated summary: {summary}")
                print(f"   📊 Based on {len(messages)} messages")
    else:
        print(f"❌ Failed to get threads for summary testing: {response.status_code}")

def run_summary_fix_tests():
    """Run comprehensive tests to verify the summary fix."""
    print("🧪 Thread Summary Fix Verification")
    print("=" * 50)
    
    # Test thread list API includes messages
    test_thread_list_api()
    
    # Test specific thread API
    test_specific_thread()
    
    # Test summary generation
    test_summary_generation()
    
    print("\n" + "=" * 50)
    print("🎉 Summary Fix Tests Completed!")
    
    print(f"\n📋 What Should Work Now:")
    print("✅ Thread list API includes full message data")
    print("✅ Frontend can generate summaries from message content")
    print("✅ Threads with no messages show description or 'New conversation'")
    print("✅ Threads with messages show intelligent topic-based summaries")
    
    print(f"\n🎯 Check Your Frontend:")
    print("1. Go to http://localhost:3000")
    print("2. Look at the dashboard thread list")
    print("3. Each thread should show a meaningful summary instead of 'No messages yet'")
    print("4. Blue summary boxes should contain relevant content")
    
    print(f"\n🔧 If Still Showing 'No messages yet':")
    print("1. Restart your frontend server (npm run dev)")
    print("2. Clear browser cache and refresh")
    print("3. Check browser console for any JavaScript errors")

if __name__ == "__main__":
    try:
        run_summary_fix_tests()
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Make sure the server is running at http://localhost:8000")
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
