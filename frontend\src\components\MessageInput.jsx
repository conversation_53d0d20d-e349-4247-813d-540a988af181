import React, { useState, useRef, useEffect } from 'react'
import { Send, Smile } from 'lucide-react'
import { getAgentInfo } from '../services/api'

function MessageInput({ onSend, onTyping, disabled, placeholder }) {
  const [message, setMessage] = useState('')
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [suggestions, setSuggestions] = useState([])
  const [selectedSuggestion, setSelectedSuggestion] = useState(-1)
  const textareaRef = useRef(null)
  const typingTimeoutRef = useRef(null)
  const { agents } = getAgentInfo()

  useEffect(() => {
    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px'
    }
  }, [message])

  useEffect(() => {
    // Handle @mention suggestions
    const lastAtIndex = message.lastIndexOf('@')
    if (lastAtIndex !== -1) {
      const query = message.slice(lastAtIndex + 1).toLowerCase()
      const matchingAgents = agents.filter(agent =>
        agent.name.toLowerCase().startsWith(query) && query.length > 0
      )
      
      if (matchingAgents.length > 0 && query.length > 0) {
        setSuggestions(matchingAgents)
        setShowSuggestions(true)
        setSelectedSuggestion(0)
      } else {
        setShowSuggestions(false)
      }
    } else {
      setShowSuggestions(false)
    }
  }, [message, agents])

  const handleSubmit = (e) => {
    e.preventDefault()
    if (message.trim() && !disabled) {
      // Stop typing indicator
      if (onTyping) {
        onTyping(false)
      }

      onSend(message.trim())
      setMessage('')
      setShowSuggestions(false)
    }
  }

  const handleMessageChange = (e) => {
    const newMessage = e.target.value
    setMessage(newMessage)

    // Handle typing indicator
    if (onTyping) {
      if (newMessage.trim() && !typingTimeoutRef.current) {
        onTyping(true)
      }

      // Clear existing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current)
      }

      // Set new timeout to stop typing indicator
      typingTimeoutRef.current = setTimeout(() => {
        onTyping(false)
        typingTimeoutRef.current = null
      }, 1000)
    }
  }

  const handleKeyDown = (e) => {
    if (showSuggestions && suggestions.length > 0) {
      if (e.key === 'ArrowDown') {
        e.preventDefault()
        setSelectedSuggestion(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        )
      } else if (e.key === 'ArrowUp') {
        e.preventDefault()
        setSelectedSuggestion(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        )
      } else if (e.key === 'Tab' || e.key === 'Enter') {
        if (selectedSuggestion >= 0) {
          e.preventDefault()
          selectSuggestion(suggestions[selectedSuggestion])
        }
      } else if (e.key === 'Escape') {
        setShowSuggestions(false)
      }
    }

    if (e.key === 'Enter' && !e.shiftKey && !showSuggestions) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  const selectSuggestion = (agent) => {
    const lastAtIndex = message.lastIndexOf('@')
    const newMessage = message.slice(0, lastAtIndex) + `@${agent.name} `
    setMessage(newMessage)
    setShowSuggestions(false)
    textareaRef.current?.focus()
  }

  const insertAgentMention = (agentName) => {
    const newMessage = message + `@${agentName} `
    setMessage(newMessage)
    textareaRef.current?.focus()
  }

  return (
    <div className="relative">
      <form onSubmit={handleSubmit} className="flex items-end space-x-3">
        <div className="flex-1 relative">
          <textarea
            ref={textareaRef}
            id="message-input"
            value={message}
            onChange={handleMessageChange}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled}
            rows={1}
            className="input-field resize-none min-h-[44px] max-h-32 pr-12"
            style={{ paddingRight: '3rem' }}
          />
          
          {/* Quick Agent Buttons */}
          <div className="absolute right-2 top-2 flex items-center space-x-1">
            <button
              type="button"
              onClick={() => setShowSuggestions(!showSuggestions)}
              className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
              title="Insert @mention"
            >
              <Smile className="w-4 h-4" />
            </button>
          </div>
        </div>

        <button
          type="submit"
          disabled={!message.trim() || disabled}
          className="btn-primary flex items-center justify-center w-11 h-11 p-0"
        >
          <Send className="w-4 h-4" />
        </button>
      </form>

      {/* Agent Suggestions Dropdown */}
      {showSuggestions && suggestions.length > 0 && (
        <div className="absolute bottom-full left-0 right-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
          <div className="p-2">
            <div className="text-xs text-gray-500 mb-2">Select an agent:</div>
            {suggestions.map((agent, index) => (
              <button
                key={agent.name}
                onClick={() => selectSuggestion(agent)}
                className={`w-full text-left p-2 rounded-lg transition-colors ${
                  index === selectedSuggestion
                    ? 'bg-primary-50 text-primary-700'
                    : 'hover:bg-gray-50'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <span className="text-lg">{agent.icon}</span>
                  <div>
                    <div className={`agent-badge agent-${agent.color}`}>
                      @{agent.name}
                    </div>
                    <div className="text-xs text-gray-600 mt-1">
                      {agent.description}
                    </div>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Quick Agent Selector (when no @mention) */}
      {!message.includes('@') && (
        <div className="mt-2 flex flex-wrap gap-2">
          {agents.slice(0, 4).map((agent) => (
            <button
              key={agent.name}
              type="button"
              onClick={() => insertAgentMention(agent.name)}
              className={`agent-badge agent-${agent.color} hover:opacity-80 transition-opacity cursor-pointer`}
            >
              {agent.icon} @{agent.name}
            </button>
          ))}
        </div>
      )}
    </div>
  )
}

export default MessageInput
