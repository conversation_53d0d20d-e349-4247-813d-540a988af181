import React from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter'
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism'
import { User, <PERSON><PERSON>, <PERSON>, Loader2 } from 'lucide-react'
import authService from '../services/authService'

function MessageList({ messages }) {
  const formatTime = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  const getAgentIcon = (author) => {
    const icons = {
      'Researcher': '🔍',
      'Analyst': '📊',
      'Summarizer': '📝',
      'Writer': '✍️',
      'Coder': '💻',
      'Translator': '🌍',
      'System': '🤖'
    }
    return icons[author] || '👤'
  }

  const getAgentBadgeClass = (author) => {
    const classes = {
      'Researcher': 'agent-researcher',
      'Analyst': 'agent-analyst',
      'Summarizer': 'agent-summarizer',
      'Writer': 'agent-writer',
      'Coder': 'agent-coder',
      'Translator': 'agent-translator',
      'System': 'agent-system'
    }
    return `agent-badge ${classes[author] || 'agent-system'}`
  }

  const isHuman = (message) => {
    return message.author_type === 'human' ||
           ['User', 'TestUser'].includes(message.author) ||
           !['Researcher', 'Analyst', 'Summarizer', 'Writer', 'Coder', 'Translator', 'System'].includes(message.author)
  }

  const getUserDisplayInfo = (username) => {
    return authService.getUserDisplayInfo(username)
  }

  const isCurrentUser = (message) => {
    const currentUser = authService.getCurrentUser()
    return currentUser && (
      message.author === currentUser.username ||
      (message.user_id && message.user_id === currentUser.id)
    )
  }

  const CodeBlock = ({ node, inline, className, children, ...props }) => {
    const match = /language-(\w+)/.exec(className || '')
    return !inline && match ? (
      <SyntaxHighlighter
        style={oneDark}
        language={match[1]}
        PreTag="div"
        className="rounded-lg"
        {...props}
      >
        {String(children).replace(/\n$/, '')}
      </SyntaxHighlighter>
    ) : (
      <code className="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono" {...props}>
        {children}
      </code>
    )
  }

  return (
    <div className="space-y-6">
      {messages.map((message, index) => {
        const isUserMessage = isHuman(message)
        const isCurrentUserMessage = isCurrentUser(message)
        const userInfo = isUserMessage ? getUserDisplayInfo(message.author) : null

        return (
          <div
            key={message.id || index}
            className={`flex ${isCurrentUserMessage ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-3xl ${
                isCurrentUserMessage
                  ? 'bg-blue-600 text-white rounded-lg rounded-br-sm'
                  : isUserMessage && !isCurrentUserMessage
                  ? 'bg-gray-100 border border-gray-300 rounded-lg rounded-bl-sm'
                  : 'bg-white border border-gray-200 rounded-lg rounded-bl-sm shadow-sm'
              } p-4`}
            >
              {/* Message Header */}
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  {isUserMessage ? (
                    <>
                      {userInfo && (
                        <div
                          className="w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-medium"
                          style={{ backgroundColor: userInfo.color }}
                        >
                          {userInfo.displayName.charAt(0)}
                        </div>
                      )}
                      <span className={`text-sm font-medium ${
                        isCurrentUserMessage ? 'text-white' : 'text-gray-900'
                      }`}>
                        {userInfo ? userInfo.displayName : message.author}
                        {isCurrentUserMessage && (
                          <span className="ml-1 text-xs opacity-75">(You)</span>
                        )}
                      </span>
                    </>
                  ) : (
                    <>
                      <span className="text-lg">{getAgentIcon(message.author)}</span>
                      <span className={getAgentBadgeClass(message.author)}>
                        {message.author}
                        {message.streaming && (
                          <Loader2 className="w-3 h-3 ml-1 animate-spin inline" />
                        )}
                      </span>
                    </>
                  )}
                </div>
                <div className={`flex items-center space-x-1 text-xs ${
                  isCurrentUserMessage ? 'text-blue-100' : isUserMessage ? 'text-gray-500' : 'text-gray-500'
                }`}>
                  <Clock className="w-3 h-3" />
                  <span>{formatTime(message.created_at)}</span>
                </div>
              </div>

              {/* Message Content */}
              <div className={`prose prose-sm max-w-none ${
                isCurrentUserMessage
                  ? 'prose-invert'
                  : isUserMessage
                  ? 'prose-gray'
                  : 'prose-gray'
              }`}>
                {isUserMessage ? (
                  <p className={`whitespace-pre-wrap ${
                    isCurrentUserMessage ? 'text-white' : 'text-gray-900'
                  }`}>
                    {message.content}
                  </p>
                ) : (
                  <ReactMarkdown
                    remarkPlugins={[remarkGfm]}
                    components={{
                      code: CodeBlock,
                      pre: ({ children }) => <div>{children}</div>,
                      h1: ({ children }) => <h1 className="text-lg font-bold mb-2">{children}</h1>,
                      h2: ({ children }) => <h2 className="text-base font-bold mb-2">{children}</h2>,
                      h3: ({ children }) => <h3 className="text-sm font-bold mb-1">{children}</h3>,
                      ul: ({ children }) => <ul className="list-disc list-inside space-y-1">{children}</ul>,
                      ol: ({ children }) => <ol className="list-decimal list-inside space-y-1">{children}</ol>,
                      blockquote: ({ children }) => (
                        <blockquote className="border-l-4 border-gray-300 pl-4 italic">{children}</blockquote>
                      ),
                      table: ({ children }) => (
                        <div className="overflow-x-auto">
                          <table className="min-w-full border border-gray-300">{children}</table>
                        </div>
                      ),
                      th: ({ children }) => (
                        <th className="border border-gray-300 px-2 py-1 bg-gray-100 font-medium">{children}</th>
                      ),
                      td: ({ children }) => (
                        <td className="border border-gray-300 px-2 py-1">{children}</td>
                      )
                    }}
                  >
                    {message.content}
                  </ReactMarkdown>
                  {message.streaming && (
                    <div className="flex items-center space-x-2 mt-2 text-sm text-gray-500">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span>Generating response...</span>
                    </div>
                  )}
                )}
              </div>
            </div>
          </div>
        )
      })}
    </div>
  )
}

export default MessageList
