"""
Quick test to verify file upload functionality is working.
"""
import requests
from pathlib import Path

BASE_URL = "http://localhost:8000"

def test_file_upload_endpoint():
    """Test the file upload endpoint directly."""
    print("🧪 Testing File Upload Endpoint")
    print("-" * 40)
    
    # Create a test markdown file
    test_content = """# Test Document for File Upload

This is a test document to verify that the file upload functionality is working correctly.

## Features to Test
- File upload via API
- Text extraction from markdown
- File association with threads
- File listing and management

## Agents Available
- @Researcher - Information gathering
- @Analyst - Strategic analysis  
- @Writer - Content creation
- @Coder - Programming solutions
- @Translator - Language translation
- @Summarizer - Content organization

## Usage Example
Once this file is uploaded, you can ask agents to analyze it:

"@Analyst What are the key features mentioned in this document?"
"@Summarizer Please summarize the content of the uploaded file"
"""
    
    test_file_path = Path("test_upload.md")
    test_file_path.write_text(test_content)
    
    try:
        # Test file upload
        with open(test_file_path, 'rb') as f:
            files = {"file": ("test_upload.md", f, "text/markdown")}
            response = requests.post(f"{BASE_URL}/api/files/upload", files=files)
        
        if response.status_code == 201:
            file_info = response.json()
            print("✅ File upload successful!")
            print(f"   File ID: {file_info['id']}")
            print(f"   Original name: {file_info['filename']}")
            print(f"   Type: {file_info['file_type']}")
            print(f"   Size: {file_info['file_size']} bytes")
            print(f"   Text extracted: {file_info['text_length']} characters")
            
            # Test file content retrieval
            content_response = requests.get(f"{BASE_URL}/api/files/{file_info['id']}/content")
            if content_response.status_code == 200:
                content_data = content_response.json()
                print("✅ File content retrieval successful!")
                print(f"   Content length: {len(content_data['content'])} characters")
                print(f"   First 100 chars: {content_data['content'][:100]}...")
            
            return file_info['id']
        else:
            print(f"❌ File upload failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return None
            
    finally:
        # Clean up test file
        if test_file_path.exists():
            test_file_path.unlink()


def test_file_with_thread():
    """Test file upload associated with a thread."""
    print("\n🧪 Testing File Upload with Thread Association")
    print("-" * 40)
    
    # Create a test thread
    thread_data = {
        "title": "File Upload Test Thread",
        "description": "Testing file upload functionality with thread association"
    }
    
    response = requests.post(f"{BASE_URL}/api/threads", json=thread_data)
    if response.status_code != 201:
        print(f"❌ Failed to create test thread: {response.status_code}")
        return
    
    thread_id = response.json()["id"]
    print(f"✅ Created test thread (ID: {thread_id})")
    
    # Create a test file
    test_content = """# Thread-Associated Document

This document is associated with a specific conversation thread.

## Purpose
- Test file upload with thread association
- Verify agents can access uploaded content
- Test file management within conversations

## Next Steps
1. Upload this file to the thread
2. Ask agents to analyze the content
3. Verify the file appears in the thread's file list
"""
    
    test_file_path = Path("thread_test.md")
    test_file_path.write_text(test_content)
    
    try:
        # Upload file with thread association
        with open(test_file_path, 'rb') as f:
            files = {"file": ("thread_test.md", f, "text/markdown")}
            data = {"thread_id": thread_id}
            response = requests.post(f"{BASE_URL}/api/files/upload", files=files, data=data)
        
        if response.status_code == 201:
            file_info = response.json()
            print("✅ File uploaded with thread association!")
            print(f"   File ID: {file_info['id']}")
            print(f"   Thread ID: {file_info['thread_id']}")
            
            # Test getting thread files
            thread_files_response = requests.get(f"{BASE_URL}/api/files/thread/{thread_id}")
            if thread_files_response.status_code == 200:
                thread_files = thread_files_response.json()
                print(f"✅ Thread files retrieved: {len(thread_files)} files found")
                for file in thread_files:
                    print(f"   - {file['original_filename']} ({file['file_type']})")
            
            return file_info['id'], thread_id
        else:
            print(f"❌ File upload with thread failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return None, thread_id
            
    finally:
        # Clean up test file
        if test_file_path.exists():
            test_file_path.unlink()


def test_file_management():
    """Test file listing and management."""
    print("\n🧪 Testing File Management")
    print("-" * 40)
    
    # List all files
    response = requests.get(f"{BASE_URL}/api/files/")
    if response.status_code == 200:
        files = response.json()
        print(f"✅ File listing successful: {len(files)} files found")
        
        for file in files[:3]:  # Show first 3 files
            print(f"   - {file['original_filename']} ({file['file_type']}, {file['file_size']} bytes)")
    else:
        print(f"❌ File listing failed: {response.status_code}")


def run_file_upload_tests():
    """Run comprehensive file upload tests."""
    print("🧪 File Upload UI Integration Test")
    print("=" * 50)
    
    # Test basic file upload
    file_id = test_file_upload_endpoint()
    
    # Test file upload with thread
    thread_file_id, thread_id = test_file_with_thread()
    
    # Test file management
    test_file_management()
    
    print("\n" + "=" * 50)
    print("🎉 File upload tests completed!")
    print("\n📋 Frontend Integration Points:")
    print("✅ Dashboard: 'Upload File' button in header")
    print("✅ Thread View: 'Upload File' button with paperclip icon")
    print("✅ File List: View and manage uploaded files")
    print("✅ File Content: Preview extracted text content")
    print("\n🎯 How to Use in Frontend:")
    print("1. Go to http://localhost:3000")
    print("2. Click 'Upload File' on dashboard or in a conversation")
    print("3. Drag & drop or browse for files (PDF, DOCX, TXT, MD, code)")
    print("4. Files are automatically processed and available to agents")
    print("5. Ask agents to analyze uploaded content with @mentions")


if __name__ == "__main__":
    try:
        run_file_upload_tests()
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Make sure the server is running at http://localhost:8000")
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
