# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=BHK6usMsp3Jxp0cbTvVUUv5VrKPNkFACbgcTq1zymH3EPtIO1UokJQQJ99BEACHYHv6XJ3w3AAAAACOGZq5y
AZURE_OPENAI_ENDPOINT=https://admk-mag2rejv-eastus2.cognitiveservices.azure.com/
AZURE_OPENAI_API_VERSION=2025-01-01-preview
AZURE_OPENAI_DEPLOYMENT=gpt-4.1




# Tavily Search API
TAVILY_API_KEY=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

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/collaborative_intelligence

# Optional: Application Configuration
DEBUG=True
LOG_LEVEL=info
