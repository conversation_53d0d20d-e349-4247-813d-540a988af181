# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview
AZURE_OPENAI_DEPLOYMENT=gpt-4

# Tavily Search API
TAVILY_API_KEY=your_tavily_api_key_here

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/collaborative_intelligence

# Optional: Application Configuration
DEBUG=True
LOG_LEVEL=info
