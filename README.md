# Collaborative Intelligence Platform

A multi-agent AI conversation platform built with FastAPI and LangGraph that enables dynamic routing to specialized AI agents using @mentions.

## 🚀 Features

- **Multi-Agent System**: Route conversations to specialized AI agents using @mentions
- **Persistent Conversations**: Threads maintain full conversation history with database storage
- **Specialized Agents**:
  - **@Researcher**: Web search and information gathering using Tavily API
  - **@Analyst**: Analysis, insights, and strategic thinking
  - **@Summarizer**: Conversation summaries and organization
- **RESTful API**: Clean FastAPI endpoints for integration
- **Azure OpenAI Integration**: Powered by Azure OpenAI services

## 🏗️ Architecture

```
User Message → FastAPI → LangGraph Router → Specialized Agent → Response
                ↓
            PostgreSQL Database (Persistent Storage)
```

### Core Components

1. **FastAPI Backend** (`main.py`): REST API server with CORS support
2. **LangGraph System** (`graph.py`): Multi-agent orchestration with state management
3. **Database Models** (`models.py`): SQLAlchemy models for threads, messages, users, tags
4. **API Router** (`api_router.py`): Endpoint handlers for thread and message operations
5. **Pydantic Schemas** (`schemas.py`): Request/response validation

## 📋 Prerequisites

- Python 3.9+
- PostgreSQL database
- Azure OpenAI account with API access
- Tavily API key for web search

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd collaborative-intelligence-platform
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your actual API keys and database URL
   ```

4. **Set up PostgreSQL database**
   ```bash
   # Create database
   createdb collaborative_intelligence
   
   # Tables will be created automatically on first run
   ```

## 🔧 Configuration

### Required Environment Variables

```bash
# Azure OpenAI
AZURE_OPENAI_API_KEY=your_azure_openai_api_key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview
AZURE_OPENAI_DEPLOYMENT=gpt-4

# Tavily Search
TAVILY_API_KEY=your_tavily_api_key

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/collaborative_intelligence
```

## 🚀 Running the Application

```bash
# Development mode with auto-reload
python main.py

# Or using uvicorn directly
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

The API will be available at:
- **Application**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

## 📖 API Usage

### 1. Create a Thread

```bash
curl -X POST "http://localhost:8000/api/threads" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Market Research Discussion",
    "description": "Analyzing renewable energy trends"
  }'
```

### 2. Send Messages with Agent Mentions

```bash
curl -X POST "http://localhost:8000/api/threads/1/messages" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "@Researcher What are the latest trends in renewable energy?",
    "author": "User"
  }'
```

### 3. Get Thread History

```bash
curl "http://localhost:8000/api/threads/1"
```

## 🤖 Agent Capabilities

### @Researcher
- Web search using Tavily API
- Information gathering and fact-checking
- Source citation and data compilation
- Market research and trend analysis

**Example**: `@Researcher What's the latest on Honeywell's spin-off?`

### @Analyst
- Strategic analysis and insights
- Pattern recognition in data
- Recommendations and implications
- Synthesis of complex information

**Example**: `@Analyst What are the implications of these market trends?`

### @Summarizer
- Conversation summarization
- Key topic identification
- Tag generation for organization
- Action item extraction

**Example**: `@Summarizer Please summarize our discussion so far`

## 🔄 LangGraph Flow

```mermaid
graph TD
    A[User Message] --> B[Router Node]
    B --> C{Parse @Mention}
    C -->|@Researcher| D[Research Agent]
    C -->|@Analyst| E[Analysis Agent]
    C -->|@Summarizer| F[Summarizer Agent]
    C -->|No Mention| G[Guidance Message]
    D --> H[Router Node]
    E --> H
    F --> H
    H --> I[End/Continue]
```

## 🗄️ Database Schema

- **threads**: Conversation threads with metadata
- **messages**: Individual messages with author tracking
- **users**: Human participants
- **tags**: Categorization tags
- **thread_tags**: Many-to-many relationship
- **thread_participants**: Many-to-many relationship

## 🧪 Testing

```bash
# Test the health endpoint
curl http://localhost:8000/health

# Test agent routing
curl -X POST "http://localhost:8000/api/threads/1/messages" \
  -H "Content-Type: application/json" \
  -d '{"content": "@Researcher test query", "author": "TestUser"}'
```

## 🚀 Deployment

For production deployment:

1. Set up a production PostgreSQL database
2. Configure environment variables securely
3. Use a production WSGI server like Gunicorn
4. Set up reverse proxy with Nginx
5. Configure CORS origins appropriately

```bash
# Production run example
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For issues and questions:
1. Check the API documentation at `/docs`
2. Review the health check at `/health`
3. Check application logs for detailed error information
