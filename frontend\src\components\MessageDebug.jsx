/**
 * Debug component to help troubleshoot message display issues
 */
import React from 'react'
import { useAuth } from '../context/AuthContext'

const MessageDebug = ({ messages, threadId }) => {
  const { user } = useAuth()
  
  if (!messages || messages.length === 0) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
        <h4 className="font-medium text-yellow-800 mb-2">🐛 Debug Info - No Messages</h4>
        <div className="text-sm text-yellow-700">
          <p><strong>Thread ID:</strong> {threadId}</p>
          <p><strong>Current User:</strong> {user ? user.username : 'Not logged in'}</p>
          <p><strong>Messages Array:</strong> {messages ? 'Empty array' : 'Null/undefined'}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
      <h4 className="font-medium text-blue-800 mb-2">🐛 Debug Info - Messages</h4>
      <div className="text-sm text-blue-700 space-y-2">
        <p><strong>Thread ID:</strong> {threadId}</p>
        <p><strong>Current User:</strong> {user ? user.username : 'Not logged in'}</p>
        <p><strong>Total Messages:</strong> {messages.length}</p>
        
        <div className="mt-3">
          <p><strong>Message Breakdown:</strong></p>
          <ul className="list-disc list-inside ml-4 space-y-1">
            {messages.map((msg, index) => (
              <li key={index}>
                <span className="font-medium">#{index + 1}</span> - 
                <span className="text-green-600"> {msg.author}</span> 
                <span className="text-gray-600"> ({msg.author_type || 'no type'})</span>: 
                <span className="italic"> "{msg.content?.substring(0, 30)}..."</span>
              </li>
            ))}
          </ul>
        </div>
        
        <div className="mt-3">
          <p><strong>Message Types:</strong></p>
          <ul className="list-disc list-inside ml-4">
            <li>Human messages: {messages.filter(m => m.author_type === 'human').length}</li>
            <li>Agent messages: {messages.filter(m => m.author_type === 'agent').length}</li>
            <li>System messages: {messages.filter(m => m.author === 'System').length}</li>
            <li>Unknown type: {messages.filter(m => !m.author_type).length}</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

export default MessageDebug
