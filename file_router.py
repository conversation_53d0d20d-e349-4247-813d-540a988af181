"""
File upload and management API endpoints.
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File as FastAPIFile, status
from sqlalchemy.orm import Session
from pathlib import Path

from models import File, Thread, User
from schemas import FileCreate, File as FileSchema, FileUploadResponse
from database import get_db
from file_service import process_uploaded_file, FileProcessor

router = APIRouter(prefix="/api/files", tags=["files"])


@router.post("/upload", response_model=FileUploadResponse, status_code=status.HTTP_201_CREATED)
async def upload_file(
    file: UploadFile = FastAPIFile(...),
    thread_id: Optional[int] = None,
    user_id: Optional[int] = None,
    db: Session = Depends(get_db)
) -> FileUploadResponse:
    """
    Upload a file and optionally associate it with a thread.
    
    Supports: PDF, DOCX, TXT, MD, PY, JS, JSON, CSV files up to 10MB.
    Automatically extracts text content for AI processing.
    """
    try:
        # Validate thread exists if provided
        if thread_id:
            thread = db.query(Thread).filter(Thread.id == thread_id).first()
            if not thread:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Thread {thread_id} not found"
                )
        
        # Validate user exists if provided
        if user_id:
            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"User {user_id} not found"
                )
        
        # Process the uploaded file
        file_info = await process_uploaded_file(file)
        
        # Save file record to database
        db_file = File(
            filename=file_info['unique_filename'],
            original_filename=file_info['original_filename'],
            file_type=file_info['file_type'],
            file_size=file_info['file_size'],
            content_text=file_info['text_content'],
            file_path=file_info['file_path'],
            uploaded_by=user_id,
            thread_id=thread_id
        )
        
        db.add(db_file)
        db.commit()
        db.refresh(db_file)
        
        return FileUploadResponse(
            id=db_file.id,
            filename=db_file.original_filename,
            file_type=db_file.file_type,
            file_size=db_file.file_size,
            text_length=len(file_info['text_content']) if file_info['text_content'] else 0,
            thread_id=thread_id,
            uploaded_by=user_id,
            created_at=db_file.created_at,
            message="File uploaded and processed successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        # Clean up uploaded file if database save fails
        if 'file_info' in locals():
            FileProcessor.delete_file(file_info['file_path'])
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload file: {str(e)}"
        )


@router.get("/thread/{thread_id}", response_model=List[FileSchema])
async def get_thread_files(
    thread_id: int,
    db: Session = Depends(get_db)
) -> List[FileSchema]:
    """Get all files associated with a thread."""
    try:
        # Validate thread exists
        thread = db.query(Thread).filter(Thread.id == thread_id).first()
        if not thread:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Thread {thread_id} not found"
            )
        
        files = db.query(File).filter(File.thread_id == thread_id).all()
        return [FileSchema.model_validate(file) for file in files]
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get thread files: {str(e)}"
        )


@router.get("/{file_id}", response_model=FileSchema)
async def get_file(
    file_id: int,
    db: Session = Depends(get_db)
) -> FileSchema:
    """Get file information by ID."""
    try:
        file = db.query(File).filter(File.id == file_id).first()
        if not file:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File {file_id} not found"
            )
        
        return FileSchema.model_validate(file)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get file: {str(e)}"
        )


@router.get("/{file_id}/content")
async def get_file_content(
    file_id: int,
    db: Session = Depends(get_db)
) -> dict:
    """Get extracted text content from a file."""
    try:
        file = db.query(File).filter(File.id == file_id).first()
        if not file:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File {file_id} not found"
            )
        
        return {
            "file_id": file.id,
            "filename": file.original_filename,
            "file_type": file.file_type,
            "content": file.content_text or "",
            "content_length": len(file.content_text) if file.content_text else 0
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get file content: {str(e)}"
        )


@router.delete("/{file_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_file(
    file_id: int,
    db: Session = Depends(get_db)
):
    """Delete a file and its associated data."""
    try:
        file = db.query(File).filter(File.id == file_id).first()
        if not file:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File {file_id} not found"
            )
        
        # Delete physical file
        FileProcessor.delete_file(file.file_path)
        
        # Delete database record
        db.delete(file)
        db.commit()
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete file: {str(e)}"
        )


@router.get("/", response_model=List[FileSchema])
async def list_files(
    skip: int = 0,
    limit: int = 100,
    user_id: Optional[int] = None,
    file_type: Optional[str] = None,
    db: Session = Depends(get_db)
) -> List[FileSchema]:
    """List files with optional filtering."""
    try:
        query = db.query(File)
        
        if user_id:
            query = query.filter(File.uploaded_by == user_id)
        
        if file_type:
            query = query.filter(File.file_type == file_type)
        
        files = query.offset(skip).limit(limit).all()
        return [FileSchema.model_validate(file) for file in files]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list files: {str(e)}"
        )
