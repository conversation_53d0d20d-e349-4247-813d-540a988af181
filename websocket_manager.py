"""
WebSocket manager for real-time communication.
"""
import json
from typing import Dict, List, Set
from fastapi import WebSocket, WebSocketDisconnect
from datetime import datetime


class ConnectionManager:
    """Manages WebSocket connections for real-time updates."""
    
    def __init__(self):
        # Store active connections by thread_id
        self.active_connections: Dict[int, Set[WebSocket]] = {}
        # Store user connections for global notifications
        self.user_connections: Dict[str, WebSocket] = {}
    
    async def connect(self, websocket: WebSocket, thread_id: int = None, user_id: str = None):
        """Accept a WebSocket connection."""
        await websocket.accept()
        
        # Add to thread-specific connections
        if thread_id:
            if thread_id not in self.active_connections:
                self.active_connections[thread_id] = set()
            self.active_connections[thread_id].add(websocket)
        
        # Add to user connections
        if user_id:
            self.user_connections[user_id] = websocket
        
        print(f"🔗 WebSocket connected - Thread: {thread_id}, User: {user_id}")
    
    def disconnect(self, websocket: WebSocket, thread_id: int = None, user_id: str = None):
        """Remove a WebSocket connection."""
        # Remove from thread connections
        if thread_id and thread_id in self.active_connections:
            self.active_connections[thread_id].discard(websocket)
            if not self.active_connections[thread_id]:
                del self.active_connections[thread_id]
        
        # Remove from user connections
        if user_id and user_id in self.user_connections:
            if self.user_connections[user_id] == websocket:
                del self.user_connections[user_id]
        
        print(f"🔌 WebSocket disconnected - Thread: {thread_id}, User: {user_id}")
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        """Send a message to a specific WebSocket connection."""
        try:
            await websocket.send_text(message)
        except Exception as e:
            print(f"❌ Failed to send personal message: {e}")
    
    async def send_to_thread(self, message: dict, thread_id: int):
        """Send a message to all connections in a thread."""
        if thread_id not in self.active_connections:
            return
        
        message_str = json.dumps(message)
        disconnected = set()
        
        for connection in self.active_connections[thread_id]:
            try:
                await connection.send_text(message_str)
            except Exception as e:
                print(f"❌ Failed to send to thread {thread_id}: {e}")
                disconnected.add(connection)
        
        # Clean up disconnected connections
        for connection in disconnected:
            self.active_connections[thread_id].discard(connection)
    
    async def send_to_user(self, message: dict, user_id: str):
        """Send a message to a specific user."""
        if user_id not in self.user_connections:
            return
        
        try:
            message_str = json.dumps(message)
            await self.user_connections[user_id].send_text(message_str)
        except Exception as e:
            print(f"❌ Failed to send to user {user_id}: {e}")
            # Remove disconnected user
            del self.user_connections[user_id]
    
    async def broadcast_to_all(self, message: dict):
        """Broadcast a message to all connected users."""
        message_str = json.dumps(message)
        disconnected = []
        
        for user_id, connection in self.user_connections.items():
            try:
                await connection.send_text(message_str)
            except Exception as e:
                print(f"❌ Failed to broadcast to user {user_id}: {e}")
                disconnected.append(user_id)
        
        # Clean up disconnected users
        for user_id in disconnected:
            del self.user_connections[user_id]
    
    def get_thread_connection_count(self, thread_id: int) -> int:
        """Get the number of active connections for a thread."""
        return len(self.active_connections.get(thread_id, set()))
    
    def get_total_connections(self) -> int:
        """Get the total number of active connections."""
        return len(self.user_connections)


# Global connection manager instance
manager = ConnectionManager()


class MessageTypes:
    """WebSocket message types."""
    NEW_MESSAGE = "new_message"
    USER_TYPING = "user_typing"
    USER_JOINED = "user_joined"
    USER_LEFT = "user_left"
    THREAD_UPDATED = "thread_updated"
    AGENT_THINKING = "agent_thinking"
    AGENT_RESPONSE = "agent_response"
    ERROR = "error"
    SYSTEM = "system"


def create_websocket_message(message_type: str, data: dict, thread_id: int = None) -> dict:
    """Create a standardized WebSocket message."""
    return {
        "type": message_type,
        "data": data,
        "thread_id": thread_id,
        "timestamp": datetime.now().isoformat()
    }


async def notify_new_message(thread_id: int, message_data: dict):
    """Notify all thread participants of a new message."""
    ws_message = create_websocket_message(
        MessageTypes.NEW_MESSAGE,
        message_data,
        thread_id
    )
    await manager.send_to_thread(ws_message, thread_id)


async def notify_user_typing(thread_id: int, user_name: str, is_typing: bool):
    """Notify thread participants when a user is typing."""
    ws_message = create_websocket_message(
        MessageTypes.USER_TYPING,
        {
            "user": user_name,
            "is_typing": is_typing
        },
        thread_id
    )
    await manager.send_to_thread(ws_message, thread_id)


async def notify_agent_thinking(thread_id: int, agent_name: str):
    """Notify that an agent is processing a request."""
    ws_message = create_websocket_message(
        MessageTypes.AGENT_THINKING,
        {
            "agent": agent_name,
            "message": f"{agent_name} is thinking..."
        },
        thread_id
    )
    await manager.send_to_thread(ws_message, thread_id)


async def notify_agent_response(thread_id: int, agent_name: str, response: str):
    """Notify of an agent response."""
    ws_message = create_websocket_message(
        MessageTypes.AGENT_RESPONSE,
        {
            "agent": agent_name,
            "response": response
        },
        thread_id
    )
    await manager.send_to_thread(ws_message, thread_id)


async def notify_thread_updated(thread_id: int, update_data: dict):
    """Notify that thread metadata was updated."""
    ws_message = create_websocket_message(
        MessageTypes.THREAD_UPDATED,
        update_data,
        thread_id
    )
    await manager.send_to_thread(ws_message, thread_id)


async def send_system_message(thread_id: int, message: str):
    """Send a system message to thread participants."""
    ws_message = create_websocket_message(
        MessageTypes.SYSTEM,
        {"message": message},
        thread_id
    )
    await manager.send_to_thread(ws_message, thread_id)
