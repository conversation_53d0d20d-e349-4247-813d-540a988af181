"""
File handling service for document upload and processing.
"""
import os
import uuid
import aiofiles
from pathlib import Path
from typing import Op<PERSON>, <PERSON><PERSON>
from fastapi import UploadFile, HTTPException
import PyPDF2
import docx
from io import BytesIO

# File upload configuration
UPLOAD_DIR = Path("uploads")
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
ALLOWED_EXTENSIONS = {
    'pdf': 'application/pdf',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'txt': 'text/plain',
    'md': 'text/markdown',
    'py': 'text/x-python',
    'js': 'text/javascript',
    'json': 'application/json',
    'csv': 'text/csv'
}

# Ensure upload directory exists
UPLOAD_DIR.mkdir(exist_ok=True)


class FileProcessor:
    """Service for processing uploaded files."""
    
    @staticmethod
    def validate_file(file: UploadFile) -> <PERSON><PERSON>[bool, str]:
        """
        Validate uploaded file.
        
        Returns:
            Tuple[bool, str]: (is_valid, error_message)
        """
        # Check file size
        if hasattr(file, 'size') and file.size > MAX_FILE_SIZE:
            return False, f"File size exceeds maximum allowed size of {MAX_FILE_SIZE // (1024*1024)}MB"
        
        # Check file extension
        if not file.filename:
            return False, "No filename provided"
        
        file_ext = file.filename.split('.')[-1].lower()
        if file_ext not in ALLOWED_EXTENSIONS:
            return False, f"File type '{file_ext}' not allowed. Supported types: {', '.join(ALLOWED_EXTENSIONS.keys())}"
        
        # Check content type
        expected_content_type = ALLOWED_EXTENSIONS[file_ext]
        if file.content_type and not file.content_type.startswith(expected_content_type.split('/')[0]):
            # Allow some flexibility in content type checking
            pass
        
        return True, ""
    
    @staticmethod
    async def save_file(file: UploadFile) -> Tuple[str, str]:
        """
        Save uploaded file to disk.
        
        Returns:
            Tuple[str, str]: (file_path, unique_filename)
        """
        # Generate unique filename
        file_ext = file.filename.split('.')[-1].lower()
        unique_filename = f"{uuid.uuid4()}.{file_ext}"
        file_path = UPLOAD_DIR / unique_filename
        
        # Save file
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
        
        return str(file_path), unique_filename
    
    @staticmethod
    def extract_text_from_pdf(file_path: str) -> str:
        """Extract text content from PDF file."""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
                return text.strip()
        except Exception as e:
            print(f"Error extracting text from PDF: {e}")
            return ""
    
    @staticmethod
    def extract_text_from_docx(file_path: str) -> str:
        """Extract text content from DOCX file."""
        try:
            doc = docx.Document(file_path)
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            return text.strip()
        except Exception as e:
            print(f"Error extracting text from DOCX: {e}")
            return ""
    
    @staticmethod
    def extract_text_from_txt(file_path: str) -> str:
        """Extract text content from text file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return file.read().strip()
        except UnicodeDecodeError:
            # Try with different encoding
            try:
                with open(file_path, 'r', encoding='latin-1') as file:
                    return file.read().strip()
            except Exception as e:
                print(f"Error reading text file: {e}")
                return ""
        except Exception as e:
            print(f"Error reading text file: {e}")
            return ""
    
    @classmethod
    def extract_text_content(cls, file_path: str, file_type: str) -> str:
        """
        Extract text content from file based on file type.
        
        Args:
            file_path: Path to the file
            file_type: File extension (pdf, docx, txt, etc.)
        
        Returns:
            Extracted text content
        """
        if file_type == 'pdf':
            return cls.extract_text_from_pdf(file_path)
        elif file_type == 'docx':
            return cls.extract_text_from_docx(file_path)
        elif file_type in ['txt', 'md', 'py', 'js', 'json', 'csv']:
            return cls.extract_text_from_txt(file_path)
        else:
            return ""
    
    @staticmethod
    def get_file_info(file: UploadFile) -> dict:
        """Get file information."""
        return {
            'filename': file.filename,
            'content_type': file.content_type,
            'size': getattr(file, 'size', 0)
        }
    
    @staticmethod
    def delete_file(file_path: str) -> bool:
        """Delete file from disk."""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                return True
            return False
        except Exception as e:
            print(f"Error deleting file: {e}")
            return False


# File processing utilities
async def process_uploaded_file(file: UploadFile) -> dict:
    """
    Process an uploaded file and return file information.
    
    Args:
        file: FastAPI UploadFile object
    
    Returns:
        Dictionary with file information and extracted content
    
    Raises:
        HTTPException: If file validation fails
    """
    processor = FileProcessor()
    
    # Validate file
    is_valid, error_message = processor.validate_file(file)
    if not is_valid:
        raise HTTPException(status_code=400, detail=error_message)
    
    # Save file
    file_path, unique_filename = await processor.save_file(file)
    
    # Extract text content
    file_ext = file.filename.split('.')[-1].lower()
    text_content = processor.extract_text_content(file_path, file_ext)
    
    # Get file info
    file_info = processor.get_file_info(file)
    
    return {
        'original_filename': file.filename,
        'unique_filename': unique_filename,
        'file_path': file_path,
        'file_type': file_ext,
        'file_size': file_info['size'],
        'content_type': file_info['content_type'],
        'text_content': text_content,
        'text_length': len(text_content) if text_content else 0
    }
