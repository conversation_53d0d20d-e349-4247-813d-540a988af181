/**
 * User authentication component with login/logout and user switching.
 */
import React, { useState, useEffect } from 'react'
import authService from '../services/authService'

const UserAuth = ({ onAuthChange }) => {
  const [user, setUser] = useState(authService.getCurrentUser())
  const [showLogin, setShowLogin] = useState(false)
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [loginForm, setLoginForm] = useState({ username: '', password: 'demo123' })
  const [registerForm, setRegisterForm] = useState({ username: '', email: '', fullName: '' })
  const [isRegistering, setIsRegistering] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    // Listen for authentication changes
    const handleAuthChange = (newUser, token) => {
      setUser(newUser)
      if (onAuthChange) {
        onAuthChange(newUser, token)
      }
    }

    authService.addAuthListener(handleAuthChange)
    
    return () => {
      authService.removeAuthListener(handleAuthChange)
    }
  }, [onAuthChange])

  const handleLogin = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    const result = await authService.login(loginForm.username, loginForm.password)
    
    if (result.success) {
      setShowLogin(false)
      setLoginForm({ username: '', password: 'demo123' })
    } else {
      setError(result.error)
    }
    
    setLoading(false)
  }

  const handleRegister = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    const result = await authService.register(
      registerForm.username,
      registerForm.email || null,
      registerForm.fullName || null
    )
    
    if (result.success) {
      setShowLogin(false)
      setIsRegistering(false)
      setRegisterForm({ username: '', email: '', fullName: '' })
    } else {
      setError(result.error)
    }
    
    setLoading(false)
  }

  const handleQuickLogin = async (username) => {
    setLoading(true)
    setError('')
    
    const result = await authService.quickLogin(username)
    
    if (result.success) {
      setShowUserMenu(false)
    } else {
      setError(result.error)
    }
    
    setLoading(false)
  }

  const handleLogout = () => {
    authService.logout()
    setShowUserMenu(false)
  }

  const getUserDisplayInfo = (username) => {
    return authService.getUserDisplayInfo(username)
  }

  const demoUsers = authService.getDemoUsers()

  if (!user) {
    return (
      <div className="relative">
        <button
          onClick={() => setShowLogin(true)}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
          <span>Login</span>
        </button>

        {showLogin && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">
                  {isRegistering ? 'Register' : 'Login'}
                </h2>
                <button
                  onClick={() => {
                    setShowLogin(false)
                    setIsRegistering(false)
                    setError('')
                  }}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {error && (
                <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                  {error}
                </div>
              )}

              {!isRegistering ? (
                <form onSubmit={handleLogin} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Username
                    </label>
                    <input
                      type="text"
                      value={loginForm.username}
                      onChange={(e) => setLoginForm({ ...loginForm, username: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Password
                    </label>
                    <input
                      type="password"
                      value={loginForm.password}
                      onChange={(e) => setLoginForm({ ...loginForm, password: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <button
                    type="submit"
                    disabled={loading}
                    className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
                  >
                    {loading ? 'Logging in...' : 'Login'}
                  </button>
                </form>
              ) : (
                <form onSubmit={handleRegister} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Username *
                    </label>
                    <input
                      type="text"
                      value={registerForm.username}
                      onChange={(e) => setRegisterForm({ ...registerForm, username: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Email
                    </label>
                    <input
                      type="email"
                      value={registerForm.email}
                      onChange={(e) => setRegisterForm({ ...registerForm, email: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Full Name
                    </label>
                    <input
                      type="text"
                      value={registerForm.fullName}
                      onChange={(e) => setRegisterForm({ ...registerForm, fullName: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <button
                    type="submit"
                    disabled={loading}
                    className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
                  >
                    {loading ? 'Registering...' : 'Register'}
                  </button>
                </form>
              )}

              <div className="mt-4 text-center">
                <button
                  onClick={() => setIsRegistering(!isRegistering)}
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  {isRegistering ? 'Already have an account? Login' : 'Need an account? Register'}
                </button>
              </div>

              <div className="mt-6 pt-4 border-t border-gray-200">
                <p className="text-sm text-gray-600 mb-3">Quick Demo Login:</p>
                <div className="grid grid-cols-2 gap-2">
                  {demoUsers.slice(0, 4).map((demoUser) => (
                    <button
                      key={demoUser.username}
                      onClick={() => handleQuickLogin(demoUser.username)}
                      disabled={loading}
                      className="px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-md disabled:opacity-50"
                    >
                      {demoUser.displayName}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    )
  }

  const userInfo = getUserDisplayInfo(user.username)

  return (
    <div className="relative">
      <button
        onClick={() => setShowUserMenu(!showUserMenu)}
        className="flex items-center space-x-2 px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
      >
        <div
          className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium"
          style={{ backgroundColor: userInfo.color }}
        >
          {userInfo.displayName.charAt(0)}
        </div>
        <span className="text-gray-700">{userInfo.displayName}</span>
        <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {showUserMenu && (
        <div className="absolute right-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
          <div className="p-3 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div
                className="w-10 h-10 rounded-full flex items-center justify-center text-white font-medium"
                style={{ backgroundColor: userInfo.color }}
              >
                {userInfo.displayName.charAt(0)}
              </div>
              <div>
                <p className="font-medium text-gray-900">{userInfo.displayName}</p>
                <p className="text-sm text-gray-500">@{user.username}</p>
              </div>
            </div>
          </div>

          <div className="p-2">
            <p className="px-3 py-2 text-sm text-gray-600 font-medium">Switch User:</p>
            {demoUsers.filter(u => u.username !== user.username).map((demoUser) => (
              <button
                key={demoUser.username}
                onClick={() => handleQuickLogin(demoUser.username)}
                disabled={loading}
                className="w-full flex items-center space-x-3 px-3 py-2 text-left hover:bg-gray-100 rounded-md disabled:opacity-50"
              >
                <div
                  className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium"
                  style={{ backgroundColor: demoUser.color }}
                >
                  {demoUser.displayName.charAt(0)}
                </div>
                <span className="text-gray-700">{demoUser.displayName}</span>
              </button>
            ))}
          </div>

          <div className="p-2 border-t border-gray-200">
            <button
              onClick={handleLogout}
              className="w-full flex items-center space-x-2 px-3 py-2 text-left text-red-600 hover:bg-red-50 rounded-md"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
              <span>Logout</span>
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default UserAuth
