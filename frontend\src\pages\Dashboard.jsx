import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Plus, MessageSquare, Clock, Users, Search, Upload, X, UserPlus } from 'lucide-react'
import { useThread } from '../context/ThreadContext'
import { useAuth } from '../context/AuthContext'
import { createThread } from '../services/api'
import ThreadList from '../components/ThreadList'
import AgentGuide from '../components/AgentGuide'
import CreateThreadModal from '../components/CreateThreadModal'
import FileUpload from '../components/FileUpload'

function Dashboard() {
  const navigate = useNavigate()
  const { threads, loading, addThread } = useThread()
  const { user, isAuthenticated } = useAuth()
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showFileUpload, setShowFileUpload] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')

  const handleCreateThread = async (threadData) => {
    try {
      const newThread = await createThread(threadData)
      addThread(newThread)
      setShowCreateModal(false)
      navigate(`/thread/${newThread.id}`)
    } catch (error) {
      console.error('Failed to create thread:', error)
      // Handle error (show toast, etc.)
    }
  }

  const setupDemoUsers = async () => {
    try {
      const response = await fetch('/api/auth/demo-setup', {
        method: 'POST'
      })
      if (response.ok) {
        const result = await response.json()
        alert('Demo users created! You can now login with: demo/demo123, alice/demo123, or bob/demo123')
      }
    } catch (error) {
      console.error('Failed to setup demo users:', error)
    }
  }

  const filteredThreads = threads.filter(thread =>
    thread.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    thread.description?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const stats = {
    totalThreads: threads.length,
    totalMessages: threads.reduce((sum, thread) => sum + (thread.message_count || 0), 0),
    activeAgents: 6
  }

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Manage your AI conversations and collaborate with specialized agents
          </p>
        </div>
        <div className="flex items-center space-x-3">
          {!isAuthenticated && (
            <button
              onClick={setupDemoUsers}
              className="btn-secondary flex items-center space-x-2"
            >
              <UserPlus className="w-5 h-5" />
              <span>Setup Demo</span>
            </button>
          )}
          <button
            onClick={() => setShowFileUpload(true)}
            className="btn-secondary flex items-center space-x-2"
          >
            <Upload className="w-5 h-5" />
            <span>Upload File</span>
          </button>
          <button
            onClick={() => setShowCreateModal(true)}
            className="btn-primary flex items-center space-x-2"
          >
            <Plus className="w-5 h-5" />
            <span>New Conversation</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Conversations</p>
              <p className="text-3xl font-bold text-gray-900">{stats.totalThreads}</p>
            </div>
            <MessageSquare className="w-8 h-8 text-primary-600" />
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Messages</p>
              <p className="text-3xl font-bold text-gray-900">{stats.totalMessages}</p>
            </div>
            <Clock className="w-8 h-8 text-green-600" />
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Agents</p>
              <p className="text-3xl font-bold text-gray-900">{stats.activeAgents}</p>
            </div>
            <Users className="w-8 h-8 text-purple-600" />
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Conversations List */}
        <div className="lg:col-span-2 space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">Recent Conversations</h2>
            <div className="relative">
              <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search conversations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input-field pl-10 w-64"
              />
            </div>
          </div>

          {loading ? (
            <div className="card p-8 text-center">
              <div className="animate-pulse-slow">
                <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Loading conversations...</p>
              </div>
            </div>
          ) : filteredThreads.length > 0 ? (
            <ThreadList threads={filteredThreads} />
          ) : (
            <div className="card p-8 text-center">
              <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {searchTerm ? 'No matching conversations' : 'No conversations yet'}
              </h3>
              <p className="text-gray-500 mb-4">
                {searchTerm 
                  ? 'Try adjusting your search terms'
                  : 'Start your first conversation with our AI agents'
                }
              </p>
              {!searchTerm && (
                <button
                  onClick={() => setShowCreateModal(true)}
                  className="btn-primary"
                >
                  Create Your First Conversation
                </button>
              )}
            </div>
          )}
        </div>

        {/* Agent Guide Sidebar */}
        <div className="lg:col-span-1">
          <AgentGuide />
        </div>
      </div>

      {/* File Upload Modal */}
      {showFileUpload && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">Upload File</h2>
              <button
                onClick={() => setShowFileUpload(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            <div className="p-6">
              <FileUpload
                onFileUploaded={(fileInfo) => {
                  console.log('File uploaded:', fileInfo)
                  setShowFileUpload(false)
                  // Optionally refresh the page or show success message
                }}
              />
            </div>
          </div>
        </div>
      )}

      {/* Create Thread Modal */}
      {showCreateModal && (
        <CreateThreadModal
          onClose={() => setShowCreateModal(false)}
          onSubmit={handleCreateThread}
        />
      )}
    </div>
  )
}

export default Dashboard
