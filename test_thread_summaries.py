"""
Test script to create conversations with different topics to verify summary generation.
"""
import requests
import time

BASE_URL = "http://localhost:8000"

def create_test_conversations():
    """Create test conversations with different topics to test summary generation."""
    print("🧪 Creating Test Conversations for Summary Testing")
    print("=" * 60)
    
    test_conversations = [
        {
            "title": "US Trade Policy Analysis",
            "description": "Analysis of current US trade policies and tariff impacts",
            "messages": [
                "@Researcher What are the current US tariff rates on Chinese goods?",
                "@Analyst How do these tariffs impact the US economy?",
                "@Researcher What are the latest trade negotiations between US and China?",
                "@Analyst What are the long-term economic implications of the trade war?"
            ]
        },
        {
            "title": "AI Platform Development",
            "description": "Technical discussion about building AI platforms",
            "messages": [
                "@Coder How do we implement multi-agent routing in Python?",
                "@Researcher What are the best practices for LangGraph implementation?",
                "@Coder Can you help me debug this FastAPI WebSocket connection?",
                "@Analyst What are the performance implications of our architecture?"
            ]
        },
        {
            "title": "Content Strategy Planning",
            "description": "Planning content strategy for Q4",
            "messages": [
                "@Writer What are the trending topics in our industry?",
                "@Researcher What content performs best on social media?",
                "@Analyst What are our competitors doing for content marketing?",
                "@Writer Can you draft a content calendar for next quarter?"
            ]
        },
        {
            "title": "Document Analysis Project",
            "description": "Analyzing uploaded business documents",
            "messages": [
                "I've uploaded our quarterly report for analysis",
                "@Analyst Please analyze the financial data in the uploaded document",
                "@Researcher What market trends are mentioned in the report?",
                "@Summarizer Can you create an executive summary of the key findings?"
            ]
        },
        {
            "title": "Multilingual Support Implementation",
            "description": "Adding translation features to the platform",
            "messages": [
                "@Translator What languages should we prioritize for our platform?",
                "@Researcher What are the most common business languages globally?",
                "@Coder How do we implement real-time translation in React?",
                "@Analyst What's the market demand for multilingual AI platforms?"
            ]
        },
        {
            "title": "Quality Assurance Testing",
            "description": "Testing platform functionality and features",
            "messages": [
                "Testing the new file upload feature",
                "@Researcher Can you verify the export functionality works correctly?",
                "Testing agent response times and accuracy",
                "@Analyst What metrics should we track for platform performance?"
            ]
        }
    ]
    
    created_threads = []
    
    for i, conv_data in enumerate(test_conversations, 1):
        print(f"\n{i}. Creating: {conv_data['title']}")
        
        # Create thread
        thread_data = {
            "title": conv_data["title"],
            "description": conv_data["description"]
        }
        
        response = requests.post(f"{BASE_URL}/api/threads", json=thread_data)
        
        if response.status_code == 201:
            thread = response.json()
            thread_id = thread["id"]
            print(f"   ✅ Thread created (ID: {thread_id})")
            
            # Add messages to the thread
            for j, message_content in enumerate(conv_data["messages"], 1):
                message_data = {
                    "content": message_content,
                    "author": "TestUser"
                }
                
                response = requests.post(f"{BASE_URL}/api/threads/{thread_id}/messages", json=message_data)
                
                if response.status_code == 200:
                    print(f"   📝 Message {j} added")
                else:
                    print(f"   ❌ Failed to add message {j}: {response.status_code}")
                
                # Brief pause between messages
                time.sleep(0.5)
            
            created_threads.append({
                "id": thread_id,
                "title": conv_data["title"],
                "topic": get_expected_topic(conv_data["title"])
            })
            
        else:
            print(f"   ❌ Failed to create thread: {response.status_code}")
        
        # Pause between thread creation
        time.sleep(1)
    
    return created_threads

def get_expected_topic(title):
    """Get expected topic category for validation."""
    if "trade" in title.lower() or "tariff" in title.lower():
        return "Trade & Economics"
    elif "development" in title.lower() or "platform" in title.lower():
        return "Technology & Programming"
    elif "content" in title.lower() or "strategy" in title.lower():
        return "Content & Writing"
    elif "document" in title.lower() or "analysis" in title.lower():
        return "Research & Analysis"
    elif "translation" in title.lower() or "multilingual" in title.lower():
        return "Translation & Language"
    elif "testing" in title.lower() or "quality" in title.lower():
        return "Testing & QA"
    else:
        return "General"

def verify_thread_summaries(created_threads):
    """Verify that threads have appropriate summaries in the frontend."""
    print(f"\n📋 Summary Verification")
    print("-" * 30)
    
    # Get all threads to see their summaries
    response = requests.get(f"{BASE_URL}/api/threads")
    
    if response.status_code == 200:
        all_threads = response.json()
        
        print(f"✅ Retrieved {len(all_threads)} threads")
        
        for thread in all_threads:
            if any(ct["id"] == thread["id"] for ct in created_threads):
                print(f"\n📄 {thread['title']}")
                print(f"   Messages: {len(thread.get('messages', []))}")
                
                # The summary will be generated on the frontend
                # Here we just verify the thread has the necessary data
                if thread.get('messages'):
                    user_messages = [msg for msg in thread['messages'] 
                                   if msg['author'] in ['User', 'TestUser']]
                    agent_messages = [msg for msg in thread['messages'] 
                                    if msg['author'] not in ['User', 'TestUser']]
                    
                    print(f"   User messages: {len(user_messages)}")
                    print(f"   Agent responses: {len(agent_messages)}")
                    
                    # Check for @mentions
                    mentions = []
                    for msg in user_messages:
                        if '@' in msg['content']:
                            import re
                            found_mentions = re.findall(r'@(\w+)', msg['content'])
                            mentions.extend(found_mentions)
                    
                    if mentions:
                        print(f"   Mentioned agents: {', '.join(set(mentions))}")
                else:
                    print(f"   ⚠️  No messages found")
    else:
        print(f"❌ Failed to retrieve threads: {response.status_code}")

def run_summary_tests():
    """Run comprehensive summary testing."""
    print("🧪 Thread Summary Generation Test")
    print("=" * 60)
    
    # Create test conversations
    created_threads = create_test_conversations()
    
    # Verify summaries
    verify_thread_summaries(created_threads)
    
    print("\n" + "=" * 60)
    print("🎉 Summary Test Completed!")
    
    print(f"\n📋 What to Check in Frontend:")
    print("✅ Go to http://localhost:3000")
    print("✅ Look at the dashboard thread list")
    print("✅ Each thread should show a blue summary box with 💡 icon")
    print("✅ Summaries should reflect the conversation topics:")
    print("   • Trade discussions → Economic/trade summaries")
    print("   • Technical discussions → Programming/development summaries")
    print("   • Content discussions → Writing/content summaries")
    print("   • Document analysis → Research/analysis summaries")
    print("   • Translation topics → Language/translation summaries")
    print("   • Testing topics → QA/testing summaries")
    
    print(f"\n🎯 Summary Features:")
    print("✅ Intelligent topic detection based on keywords")
    print("✅ Agent mention analysis")
    print("✅ Conversation pattern recognition")
    print("✅ Fallback to first user message if no clear topic")
    print("✅ Caching for performance")
    print("✅ Visual highlighting with blue background")
    
    print(f"\n📊 Created {len(created_threads)} test conversations:")
    for thread in created_threads:
        print(f"   • {thread['title']} (Expected: {thread['topic']})")

if __name__ == "__main__":
    try:
        run_summary_tests()
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Make sure the server is running at http://localhost:8000")
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
