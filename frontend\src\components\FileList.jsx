import React, { useState, useEffect } from 'react'
import { File, Download, Eye, Trash2, FileText, Code, Image, X } from 'lucide-react'

function FileList({ threadId, files: propFiles, onFileDeleted }) {
  const [files, setFiles] = useState(propFiles || [])
  const [loading, setLoading] = useState(!propFiles)
  const [selectedFile, setSelectedFile] = useState(null)
  const [fileContent, setFileContent] = useState(null)
  const [showContent, setShowContent] = useState(false)

  useEffect(() => {
    if (!propFiles && threadId) {
      loadFiles()
    }
  }, [threadId, propFiles])

  const loadFiles = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/files/thread/${threadId}`)
      if (response.ok) {
        const data = await response.json()
        setFiles(data)
      }
    } catch (error) {
      console.error('Failed to load files:', error)
    } finally {
      setLoading(false)
    }
  }

  const getFileIcon = (fileType) => {
    const iconMap = {
      'pdf': <FileText className="w-4 h-4 text-red-600" />,
      'docx': <FileText className="w-4 h-4 text-blue-600" />,
      'txt': <FileText className="w-4 h-4 text-gray-600" />,
      'md': <FileText className="w-4 h-4 text-purple-600" />,
      'py': <Code className="w-4 h-4 text-green-600" />,
      'js': <Code className="w-4 h-4 text-yellow-600" />,
      'json': <Code className="w-4 h-4 text-orange-600" />,
      'csv': <FileText className="w-4 h-4 text-green-600" />
    }
    return iconMap[fileType] || <File className="w-4 h-4 text-gray-600" />
  }

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const handleViewContent = async (file) => {
    try {
      setSelectedFile(file)
      setFileContent(null)
      setShowContent(true)
      
      const response = await fetch(`/api/files/${file.id}/content`)
      if (response.ok) {
        const data = await response.json()
        setFileContent(data.content)
      }
    } catch (error) {
      console.error('Failed to load file content:', error)
    }
  }

  const handleDeleteFile = async (fileId) => {
    if (!confirm('Are you sure you want to delete this file?')) {
      return
    }

    try {
      const response = await fetch(`/api/files/${fileId}`, {
        method: 'DELETE'
      })
      
      if (response.ok) {
        setFiles(files.filter(f => f.id !== fileId))
        if (onFileDeleted) {
          onFileDeleted(fileId)
        }
      }
    } catch (error) {
      console.error('Failed to delete file:', error)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-pulse text-gray-500">Loading files...</div>
      </div>
    )
  }

  if (files.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <File className="w-12 h-12 mx-auto mb-2 text-gray-300" />
        <p>No files uploaded yet</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* File List */}
      <div className="space-y-2">
        {files.map((file) => (
          <div
            key={file.id}
            className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
          >
            <div className="flex items-center space-x-3 flex-1 min-w-0">
              {getFileIcon(file.file_type)}
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {file.original_filename}
                </p>
                <div className="flex items-center space-x-4 text-xs text-gray-500">
                  <span>{formatFileSize(file.file_size)}</span>
                  <span>{formatDate(file.created_at)}</span>
                  {file.content_text && (
                    <span>{file.content_text.length} chars extracted</span>
                  )}
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              {file.content_text && (
                <button
                  onClick={() => handleViewContent(file)}
                  className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                  title="View content"
                >
                  <Eye className="w-4 h-4" />
                </button>
              )}
              
              <button
                onClick={() => handleDeleteFile(file.id)}
                className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                title="Delete file"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Content Modal */}
      {showContent && selectedFile && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[80vh] flex flex-col">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                {getFileIcon(selectedFile.file_type)}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    {selectedFile.original_filename}
                  </h3>
                  <p className="text-sm text-gray-500">
                    {formatFileSize(selectedFile.file_size)} • {formatDate(selectedFile.created_at)}
                  </p>
                </div>
              </div>
              <button
                onClick={() => setShowContent(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Content */}
            <div className="flex-1 overflow-auto p-4">
              {fileContent ? (
                <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono">
                  {fileContent}
                </pre>
              ) : (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-pulse text-gray-500">Loading content...</div>
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="p-4 border-t border-gray-200 bg-gray-50">
              <p className="text-xs text-gray-600">
                💡 This content has been extracted and is available to AI agents in this conversation.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default FileList
