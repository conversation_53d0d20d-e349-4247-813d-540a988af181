"""
Pydantic schemas for API request/response validation.
"""
from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field


class UserBase(BaseModel):
    """Base user schema."""
    username: str = Field(..., min_length=1, max_length=50)
    email: Optional[str] = Field(None, max_length=100)
    full_name: Optional[str] = Field(None, max_length=100)


class UserCreate(UserBase):
    """Schema for creating a user."""
    pass


class User(UserBase):
    """Schema for user response."""
    id: int
    created_at: datetime

    model_config = {"from_attributes": True}


class TagBase(BaseModel):
    """Base tag schema."""
    name: str = Field(..., min_length=1, max_length=50)
    description: Optional[str] = None


class TagCreate(TagBase):
    """Schema for creating a tag."""
    pass


class Tag(TagBase):
    """Schema for tag response."""
    id: int
    created_at: datetime

    model_config = {"from_attributes": True}


class MessageBase(BaseModel):
    """Base message schema."""
    content: str = Field(..., min_length=1)
    author: str = Field(..., min_length=1, max_length=100)
    author_type: str = Field(default="human", regex="^(human|agent)$")


class MessageCreate(BaseModel):
    """Schema for creating a message."""
    content: str = Field(..., min_length=1)
    author: str = Field(default="User", max_length=100)


class Message(MessageBase):
    """Schema for message response."""
    id: int
    thread_id: int
    user_id: Optional[int] = None
    created_at: datetime

    model_config = {"from_attributes": True}


class ThreadBase(BaseModel):
    """Base thread schema."""
    title: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None


class ThreadCreate(ThreadBase):
    """Schema for creating a thread."""
    pass


class Thread(ThreadBase):
    """Schema for thread response."""
    id: int
    created_at: datetime
    updated_at: datetime
    messages: List[Message] = []
    tags: List[Tag] = []
    participants: List[User] = []

    model_config = {"from_attributes": True}


class ThreadSummary(BaseModel):
    """Schema for thread summary (without full message history)."""
    id: int
    title: str
    description: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    message_count: int
    tags: List[Tag] = []
    participants: List[User] = []

    model_config = {"from_attributes": True}


class MessageResponse(BaseModel):
    """Schema for message response from agents."""
    content: str
    author: str
    author_type: str = "agent"
    thread_id: int


class AgentMentionRequest(BaseModel):
    """Schema for handling @mention requests."""
    content: str = Field(..., min_length=1)
    author: str = Field(default="User", max_length=100)
    thread_id: int
