import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000'

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Add auth token to requests if available
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// Request interceptor for debugging
api.interceptors.request.use(
  (config) => {
    console.log('🔄 API Request:', config.method?.toUpperCase(), config.url)
    return config
  },
  (error) => {
    console.error('❌ API Request Error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor for debugging
api.interceptors.response.use(
  (response) => {
    console.log('✅ API Response:', response.status, response.config.url)
    return response
  },
  (error) => {
    console.error('❌ API Response Error:', error.response?.status, error.response?.data)
    return Promise.reject(error)
  }
)

// Health check
export const checkHealth = async () => {
  const response = await api.get('/health')
  return response.data
}

// Thread operations
export const createThread = async (threadData) => {
  const response = await api.post('/api/threads', threadData)
  return response.data
}

export const getThreads = async () => {
  const response = await api.get('/api/threads')
  return response.data
}

export const getThread = async (threadId) => {
  const response = await api.get(`/api/threads/${threadId}`)
  return response.data
}

// Message operations
export const sendMessage = async (threadId, messageData) => {
  const response = await api.post(`/api/threads/${threadId}/messages`, messageData)
  return response.data
}

// Agent information
export const getAgentInfo = () => {
  return {
    agents: [
      {
        name: 'Researcher',
        mention: '@Researcher',
        description: 'Web search and information gathering',
        icon: '🔍',
        color: 'blue',
        examples: [
          'What are the latest developments in AI?',
          'Research market trends for renewable energy',
          'Find information about quantum computing'
        ]
      },
      {
        name: 'Analyst',
        mention: '@Analyst',
        description: 'Analysis, insights, and strategic thinking',
        icon: '📊',
        color: 'green',
        examples: [
          'Analyze the implications of these findings',
          'What are the business opportunities here?',
          'Provide strategic recommendations'
        ]
      },
      {
        name: 'Summarizer',
        mention: '@Summarizer',
        description: 'Conversation summaries and organization',
        icon: '📝',
        color: 'purple',
        examples: [
          'Summarize our discussion so far',
          'Create key takeaways from this conversation',
          'Generate tags for this thread'
        ]
      },
      {
        name: 'Writer',
        mention: '@Writer',
        description: 'Creative and professional writing tasks',
        icon: '✍️',
        color: 'orange',
        examples: [
          'Write a blog post about AI trends',
          'Create a product description',
          'Draft a professional email'
        ]
      },
      {
        name: 'Coder',
        mention: '@Coder',
        description: 'Programming, debugging, and technical solutions',
        icon: '💻',
        color: 'red',
        examples: [
          'Create a Python function to sort data',
          'Help debug this JavaScript code',
          'Design a REST API structure'
        ]
      },
      {
        name: 'Translator',
        mention: '@Translator',
        description: 'Language translation and localization',
        icon: '🌍',
        color: 'indigo',
        examples: [
          'Translate this text to Spanish',
          'Convert this document to French',
          'Localize this content for German market'
        ]
      }
    ]
  }
}

export default api
