"""
Authentication API endpoints.
"""
from datetime import <PERSON><PERSON><PERSON>
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON>earer
from sqlalchemy.orm import Session
from pydantic import BaseModel

from models import User
from schemas import UserCreate, User as UserSchema
from database import get_db
from auth_service import AuthService, get_current_user, ACCESS_TOKEN_EXPIRE_MINUTES

router = APIRouter(prefix="/api/auth", tags=["authentication"])


class LoginRequest(BaseModel):
    """Login request schema."""
    username: str
    password: str


class TokenResponse(BaseModel):
    """Token response schema."""
    access_token: str
    token_type: str
    expires_in: int
    user: UserSchema


class UserRegistration(BaseModel):
    """User registration schema."""
    username: str
    email: Optional[str] = None
    full_name: Optional[str] = None
    password: str = "demo123"  # Demo password for simplicity


@router.post("/login", response_model=TokenResponse)
async def login(
    login_data: LoginRequest,
    db: Session = Depends(get_db)
) -> TokenResponse:
    """
    Authenticate user and return access token.
    
    Demo credentials:
    - Username: demo, alice, bob
    - Password: demo123
    """
    try:
        # Authenticate user
        user = AuthService.authenticate_user(db, login_data.username, login_data.password)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect username or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Create access token
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = AuthService.create_access_token(
            data={"sub": user.username},
            expires_delta=access_token_expires
        )
        
        return TokenResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=ACCESS_TOKEN_EXPIRE_MINUTES * 60,  # Convert to seconds
            user=UserSchema.model_validate(user)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Login failed: {str(e)}"
        )


@router.post("/register", response_model=UserSchema, status_code=status.HTTP_201_CREATED)
async def register(
    user_data: UserRegistration,
    db: Session = Depends(get_db)
) -> UserSchema:
    """
    Register a new user.
    
    For demo purposes, all users get the same password: demo123
    """
    try:
        user = AuthService.create_user(
            db=db,
            username=user_data.username,
            email=user_data.email,
            full_name=user_data.full_name
        )
        
        return UserSchema.model_validate(user)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Registration failed: {str(e)}"
        )


@router.get("/me", response_model=UserSchema)
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
) -> UserSchema:
    """Get current user information."""
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated"
        )
    
    return UserSchema.model_validate(current_user)


@router.get("/users", response_model=list[UserSchema])
async def list_users(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
) -> list[UserSchema]:
    """List all users (for demo purposes)."""
    try:
        users = db.query(User).offset(skip).limit(limit).all()
        return [UserSchema.model_validate(user) for user in users]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list users: {str(e)}"
        )


@router.post("/demo-setup")
async def setup_demo_users(db: Session = Depends(get_db)):
    """Set up demo users for testing."""
    try:
        from auth_service import create_demo_users
        create_demo_users(db)
        
        return {
            "message": "Demo users created successfully",
            "users": [
                {"username": "demo", "password": "demo123"},
                {"username": "alice", "password": "demo123"},
                {"username": "bob", "password": "demo123"}
            ]
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create demo users: {str(e)}"
        )
