"""
Test script to verify improved thread summaries are concise and meaningful.
"""
import requests
import time

BASE_URL = "http://localhost:8000"

def create_test_conversation_with_realistic_content():
    """Create a test conversation that mimics the real conversations shown in the image."""
    print("🧪 Creating Realistic Test Conversations")
    print("-" * 45)
    
    test_conversations = [
        {
            "title": "Translation Help Request",
            "description": "Getting help with translating phrases",
            "messages": [
                "Can you help me translate 'Good morning' into Spanish, French, and German?",
                "@Translator I need these translations for a business presentation",
                "Also, please include cultural context and pronunciation notes"
            ]
        },
        {
            "title": "Economic Policy Analysis",
            "description": "Analyzing current trade policies",
            "messages": [
                "@Researcher What are the current US tariff rates on Chinese imports?",
                "@Analyst How do these tariffs affect the US economy?",
                "I need this analysis for a policy report"
            ]
        },
        {
            "title": "Platform Feature Testing",
            "description": "Testing export and summary features",
            "messages": [
                "Testing the new export functionality",
                "@Summarizer Can you summarize this conversation?",
                "I want to verify all features work correctly"
            ]
        },
        {
            "title": "Code Review Session",
            "description": "Getting help with programming issues",
            "messages": [
                "@Coder I'm having trouble with my React component",
                "The state is not updating properly when I click the button",
                "@Coder Can you help me debug this issue?"
            ]
        }
    ]
    
    created_threads = []
    
    for i, conv_data in enumerate(test_conversations, 1):
        print(f"\n{i}. Creating: {conv_data['title']}")
        
        # Create thread
        thread_data = {
            "title": conv_data["title"],
            "description": conv_data["description"]
        }
        
        response = requests.post(f"{BASE_URL}/api/threads", json=thread_data)
        
        if response.status_code == 201:
            thread = response.json()
            thread_id = thread["id"]
            print(f"   ✅ Thread created (ID: {thread_id})")
            
            # Add messages to the thread
            for j, message_content in enumerate(conv_data["messages"], 1):
                message_data = {
                    "content": message_content,
                    "author": "User"
                }
                
                response = requests.post(f"{BASE_URL}/api/threads/{thread_id}/messages", json=message_data)
                
                if response.status_code == 200:
                    print(f"   📝 Message {j} added")
                    # Let agent respond
                    time.sleep(1)
                else:
                    print(f"   ❌ Failed to add message {j}: {response.status_code}")
                
                # Brief pause between messages
                time.sleep(0.5)
            
            created_threads.append({
                "id": thread_id,
                "title": conv_data["title"],
                "expected_summary_type": get_expected_summary_type(conv_data["title"])
            })
            
        else:
            print(f"   ❌ Failed to create thread: {response.status_code}")
        
        # Pause between thread creation
        time.sleep(1)
    
    return created_threads

def get_expected_summary_type(title):
    """Get expected summary type for validation."""
    if "translation" in title.lower():
        return "Translation"
    elif "economic" in title.lower() or "policy" in title.lower():
        return "Economic Analysis"
    elif "testing" in title.lower() or "feature" in title.lower():
        return "Testing"
    elif "code" in title.lower() or "programming" in title.lower():
        return "Programming"
    else:
        return "General"

def test_summary_quality():
    """Test the quality and conciseness of generated summaries."""
    print(f"\n📊 Testing Summary Quality")
    print("-" * 30)
    
    response = requests.get(f"{BASE_URL}/api/threads")
    
    if response.status_code == 200:
        threads = response.json()
        
        print(f"✅ Retrieved {len(threads)} threads for summary testing")
        
        for thread in threads:
            print(f"\n📄 Thread: {thread['title']}")
            
            # Simulate frontend summary generation
            messages = thread.get('messages', [])
            
            if len(messages) > 0:
                # Check message content types
                user_messages = [msg for msg in messages if msg['author'] in ['User', 'TestUser']]
                agent_messages = [msg for msg in messages if msg['author'] not in ['User', 'TestUser']]
                
                print(f"   📊 Messages: {len(user_messages)} user, {len(agent_messages)} agent")
                
                # Check for long agent responses (the problem we're fixing)
                long_agent_responses = [msg for msg in agent_messages if len(msg['content']) > 200]
                if long_agent_responses:
                    print(f"   ⚠️  {len(long_agent_responses)} long agent responses detected")
                    print(f"   📝 Sample long response: {long_agent_responses[0]['content'][:100]}...")
                
                # Test summary generation logic
                if user_messages:
                    first_user_msg = user_messages[0]['content'].lower()
                    
                    # Predict what summary should be generated
                    if 'translate' in first_user_msg:
                        expected = "Translation request"
                    elif 'tariff' in first_user_msg or 'economic' in first_user_msg:
                        expected = "Economic analysis and trade policy discussion"
                    elif 'test' in first_user_msg:
                        expected = "Testing platform features and functionality"
                    elif 'code' in first_user_msg or 'debug' in first_user_msg:
                        expected = "Programming assistance"
                    else:
                        expected = "General conversation"
                    
                    print(f"   💡 Expected summary: {expected}")
            else:
                print(f"   📭 No messages - should show description or 'New conversation'")

def verify_no_long_content_display():
    """Verify that long agent responses are not being displayed as summaries."""
    print(f"\n🔍 Verifying No Long Content in Summaries")
    print("-" * 45)
    
    response = requests.get(f"{BASE_URL}/api/threads")
    
    if response.status_code == 200:
        threads = response.json()
        
        issues_found = []
        
        for thread in threads:
            messages = thread.get('messages', [])
            
            # Look for threads with long agent responses
            agent_messages = [msg for msg in messages if msg['author'] not in ['User', 'TestUser']]
            
            for msg in agent_messages:
                if len(msg['content']) > 150:  # Long response threshold
                    issues_found.append({
                        'thread': thread['title'],
                        'author': msg['author'],
                        'content_length': len(msg['content']),
                        'content_preview': msg['content'][:100] + '...'
                    })
        
        if issues_found:
            print(f"⚠️  Found {len(issues_found)} long agent responses that should NOT appear as summaries:")
            for issue in issues_found[:3]:  # Show first 3
                print(f"   • {issue['thread']} - {issue['author']}: {issue['content_length']} chars")
                print(f"     Preview: {issue['content_preview']}")
        else:
            print("✅ No problematic long responses found")
    else:
        print(f"❌ Failed to get threads: {response.status_code}")

def run_improved_summary_tests():
    """Run comprehensive tests for improved summaries."""
    print("🧪 Improved Thread Summary Testing")
    print("=" * 50)
    
    # Create realistic test conversations
    created_threads = create_test_conversation_with_realistic_content()
    
    # Test summary quality
    test_summary_quality()
    
    # Verify no long content is displayed
    verify_no_long_content_display()
    
    print("\n" + "=" * 50)
    print("🎉 Improved Summary Tests Completed!")
    
    print(f"\n📋 What Should Be Fixed Now:")
    print("✅ Summaries are concise (under 80 characters)")
    print("✅ No long agent responses shown as summaries")
    print("✅ Summaries reflect conversation purpose, not content")
    print("✅ Clear topic-based categorization")
    
    print(f"\n🎯 Expected Summary Examples:")
    print("• 'Translation Help Request' → 'Translation request'")
    print("• 'Economic Policy Analysis' → 'Economic analysis and trade policy discussion'")
    print("• 'Platform Feature Testing' → 'Testing platform features and functionality'")
    print("• 'Code Review Session' → 'Programming assistance'")
    
    print(f"\n🔧 Frontend Changes Made:")
    print("✅ Removed last message content display")
    print("✅ Improved summary generation logic")
    print("✅ Added conversation purpose detection")
    print("✅ Better topic categorization")
    
    print(f"\n🎯 Check Your Frontend Now:")
    print("1. Refresh http://localhost:3000 or http://**************:3000")
    print("2. Look for SHORT, meaningful summaries in blue boxes")
    print("3. No more long agent responses as summaries")
    print("4. Clear, concise descriptions of conversation purpose")

if __name__ == "__main__":
    try:
        run_improved_summary_tests()
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Make sure the server is running at http://localhost:8000")
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
