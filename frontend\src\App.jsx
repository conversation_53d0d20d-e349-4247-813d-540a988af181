import React from 'react'
import { Routes, Route } from 'react-router-dom'
import Layout from './components/Layout'
import Dashboard from './pages/Dashboard'
import ThreadView from './pages/ThreadView'
import { ThreadProvider } from './context/ThreadContext'
import { AuthProvider } from './context/AuthContext'

function App() {
  return (
    <AuthProvider>
      <ThreadProvider>
        <Layout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/thread/:threadId" element={<ThreadView />} />
          </Routes>
        </Layout>
      </ThreadProvider>
    </AuthProvider>
  )
}

export default App
