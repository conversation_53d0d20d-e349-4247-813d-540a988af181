"""
Test script to verify frontend authentication integration.
"""
import requests
import time

BASE_URL = "http://localhost:8000"

def test_auth_endpoints():
    """Test authentication endpoints."""
    print("🔐 Testing Authentication Endpoints")
    print("-" * 40)
    
    # Test demo setup
    response = requests.post(f"{BASE_URL}/api/auth/demo-setup")
    if response.status_code == 200:
        result = response.json()
        print("✅ Demo users setup successful")
        print(f"   Available users: {[u['username'] for u in result['users']]}")
    else:
        print(f"❌ Demo setup failed: {response.status_code}")
    
    # Test login
    login_data = {"username": "demo", "password": "demo123"}
    response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
    
    if response.status_code == 200:
        token_info = response.json()
        print("✅ Login successful")
        print(f"   User: {token_info['user']['username']}")
        print(f"   Token type: {token_info['token_type']}")
        print(f"   Expires in: {token_info['expires_in']} seconds")
        
        # Test authenticated request
        headers = {"Authorization": f"Bearer {token_info['access_token']}"}
        response = requests.get(f"{BASE_URL}/api/auth/me", headers=headers)
        
        if response.status_code == 200:
            user_info = response.json()
            print("✅ Authenticated request successful")
            print(f"   Current user: {user_info['username']}")
        else:
            print(f"❌ Authenticated request failed: {response.status_code}")
        
        return token_info['access_token']
    else:
        print(f"❌ Login failed: {response.status_code}")
        return None


def test_protected_file_upload(token):
    """Test file upload with authentication."""
    print("\n📁 Testing Authenticated File Upload")
    print("-" * 40)
    
    if not token:
        print("❌ No token available for testing")
        return
    
    # Create test file
    test_content = "# Authenticated Upload Test\n\nThis file was uploaded with authentication."
    
    import tempfile
    import os
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as f:
        f.write(test_content)
        temp_file_path = f.name
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        
        with open(temp_file_path, 'rb') as f:
            files = {"file": ("auth_test.md", f, "text/markdown")}
            response = requests.post(f"{BASE_URL}/api/files/upload", files=files, headers=headers)
        
        if response.status_code == 201:
            file_info = response.json()
            print("✅ Authenticated file upload successful")
            print(f"   File ID: {file_info['id']}")
            print(f"   Uploaded by: {file_info.get('uploaded_by', 'Not specified')}")
        else:
            print(f"❌ Authenticated file upload failed: {response.status_code}")
            print(f"   Error: {response.text}")
    
    finally:
        # Clean up temp file
        os.unlink(temp_file_path)


def test_user_registration():
    """Test user registration."""
    print("\n👤 Testing User Registration")
    print("-" * 30)
    
    # Generate unique username
    import random
    username = f"testuser{random.randint(1000, 9999)}"
    
    registration_data = {
        "username": username,
        "email": f"{username}@example.com",
        "full_name": f"Test User {username[-4:]}",
        "password": "demo123"
    }
    
    response = requests.post(f"{BASE_URL}/api/auth/register", json=registration_data)
    
    if response.status_code == 201:
        user_info = response.json()
        print("✅ User registration successful")
        print(f"   Username: {user_info['username']}")
        print(f"   Email: {user_info['email']}")
        
        # Test login with new user
        login_data = {"username": username, "password": "demo123"}
        response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        
        if response.status_code == 200:
            print("✅ Login with new user successful")
        else:
            print(f"❌ Login with new user failed: {response.status_code}")
    else:
        print(f"❌ User registration failed: {response.status_code}")
        print(f"   Error: {response.text}")


def test_user_listing():
    """Test user listing endpoint."""
    print("\n👥 Testing User Listing")
    print("-" * 25)
    
    response = requests.get(f"{BASE_URL}/api/auth/users")
    
    if response.status_code == 200:
        users = response.json()
        print(f"✅ User listing successful: {len(users)} users found")
        for user in users[:5]:  # Show first 5 users
            print(f"   - {user['username']} ({user['email'] or 'no email'})")
    else:
        print(f"❌ User listing failed: {response.status_code}")


def run_frontend_auth_tests():
    """Run comprehensive frontend authentication tests."""
    print("🧪 Frontend Authentication Integration Test")
    print("=" * 60)
    
    # Test auth endpoints
    token = test_auth_endpoints()
    
    # Test protected file upload
    test_protected_file_upload(token)
    
    # Test user registration
    test_user_registration()
    
    # Test user listing
    test_user_listing()
    
    print("\n" + "=" * 60)
    print("🎉 Frontend Authentication Tests Completed!")
    
    print("\n📋 Frontend Integration Summary:")
    print("✅ Login modal in header (Sign In button)")
    print("✅ User menu with logout when authenticated")
    print("✅ Demo user setup button on dashboard")
    print("✅ Authentication context throughout app")
    print("✅ Protected file uploads with user association")
    
    print(f"\n🎯 How to Use Authentication in Frontend:")
    print("1. Go to http://localhost:3000")
    print("2. Click 'Sign In' button in the header")
    print("3. Use demo credentials: demo / demo123")
    print("4. Or click 'Setup Demo' to create demo users")
    print("5. See user menu in header when logged in")
    print("6. File uploads now associate with your user account")
    
    print(f"\n🔑 Available Demo Accounts:")
    print("- Username: demo, Password: demo123")
    print("- Username: alice, Password: demo123") 
    print("- Username: bob, Password: demo123")


if __name__ == "__main__":
    try:
        run_frontend_auth_tests()
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Make sure the server is running at http://localhost:8000")
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
