import React, { useState, useRef, useEffect } from 'react'
import { User, LogOut, Settings, ChevronDown, Users } from 'lucide-react'
import { useAuth } from '../context/AuthContext'
import authService from '../services/authService'

function UserMenu() {
  const [isOpen, setIsOpen] = useState(false)
  const [showUserSwitch, setShowUserSwitch] = useState(false)
  const { user, logout, login } = useAuth()
  const menuRef = useRef(null)

  useEffect(() => {
    function handleClickOutside(event) {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const handleLogout = () => {
    logout()
    setIsOpen(false)
  }

  const handleUserSwitch = async (username) => {
    const result = await login(username, 'demo123')
    if (result.success) {
      setIsOpen(false)
      setShowUserSwitch(false)
    }
  }

  const getUserDisplayInfo = (username) => {
    return authService.getUserDisplayInfo(username)
  }

  const demoUsers = authService.getDemoUsers()

  if (!user) return null

  const userInfo = getUserDisplayInfo(user.username)

  return (
    <div className="relative" ref={menuRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors"
      >
        <div
          className="flex items-center justify-center w-8 h-8 text-white rounded-full text-sm font-medium"
          style={{ backgroundColor: userInfo.color }}
        >
          {userInfo.displayName.charAt(0)}
        </div>
        <span className="text-sm font-medium text-gray-700">{userInfo.displayName}</span>
        <ChevronDown className="w-4 h-4 text-gray-400" />
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
          <div className="px-4 py-3 border-b border-gray-100">
            <div className="flex items-center space-x-3">
              <div
                className="flex items-center justify-center w-10 h-10 text-white rounded-full font-medium"
                style={{ backgroundColor: userInfo.color }}
              >
                {userInfo.displayName.charAt(0)}
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">{userInfo.displayName}</p>
                <p className="text-xs text-gray-500">@{user.username}</p>
                {user.email && (
                  <p className="text-xs text-gray-500">{user.email}</p>
                )}
              </div>
            </div>
          </div>

          <button
            onClick={() => setIsOpen(false)}
            className="flex items-center space-x-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
          >
            <User className="w-4 h-4" />
            <span>Profile</span>
          </button>

          <button
            onClick={() => {
              setShowUserSwitch(!showUserSwitch)
            }}
            className="flex items-center space-x-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
          >
            <Users className="w-4 h-4" />
            <span>Switch User</span>
            <ChevronDown className={`w-4 h-4 ml-auto transition-transform ${showUserSwitch ? 'rotate-180' : ''}`} />
          </button>

          {showUserSwitch && (
            <div className="px-2 py-1 bg-gray-50">
              {demoUsers.filter(u => u.username !== user.username).map((demoUser) => (
                <button
                  key={demoUser.username}
                  onClick={() => handleUserSwitch(demoUser.username)}
                  className="flex items-center space-x-3 w-full px-3 py-2 text-sm text-gray-700 hover:bg-white rounded-md transition-colors"
                >
                  <div
                    className="flex items-center justify-center w-6 h-6 text-white rounded-full text-xs font-medium"
                    style={{ backgroundColor: demoUser.color }}
                  >
                    {demoUser.displayName.charAt(0)}
                  </div>
                  <span>{demoUser.displayName}</span>
                </button>
              ))}
            </div>
          )}

          <button
            onClick={() => setIsOpen(false)}
            className="flex items-center space-x-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
          >
            <Settings className="w-4 h-4" />
            <span>Settings</span>
          </button>

          <hr className="my-1" />

          <button
            onClick={handleLogout}
            className="flex items-center space-x-2 w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
          >
            <LogOut className="w-4 h-4" />
            <span>Sign Out</span>
          </button>
        </div>
      )}
    </div>
  )
}

export default UserMenu
