"""
FastAPI router for the collaborative intelligence platform.

This module implements the main API endpoints:
- POST /api/threads: Create new conversation threads
- POST /api/threads/{thread_id}/messages: Handle message interactions with LangGraph
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from langchain_core.messages import HumanMessage

from models import Thread, Message
from schemas import (
    ThreadCreate, Thread as ThreadSchema,
    MessageCreate, MessageResponse
)
from graph import create_conversation_graph, ConversationState
from database import get_db
from export_service import export_conversation
from websocket_manager import notify_new_message, notify_agent_thinking

router = APIRouter(prefix="/api", tags=["threads"])


# ============================================================================
# THREAD ENDPOINTS
# ============================================================================

@router.post("/threads", response_model=ThreadSchema, status_code=status.HTTP_201_CREATED)
async def create_thread(
    thread_data: ThreadCreate,
    db: Session = Depends(get_db)
) -> ThreadSchema:
    """
    Create a new conversation thread.
    
    This endpoint creates an empty thread that can then receive messages.
    The thread will be ready for multi-agent conversations with @mention routing.
    """
    try:
        # Create new thread in database
        db_thread = Thread(
            title=thread_data.title,
            description=thread_data.description
        )
        
        db.add(db_thread)
        db.commit()
        db.refresh(db_thread)
        
        return ThreadSchema.model_validate(db_thread)
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create thread: {str(e)}"
        )


@router.get("/threads", response_model=List[ThreadSchema])
async def list_threads(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
) -> List[ThreadSchema]:
    """
    List all conversation threads with full message history for summary generation.

    Returns a paginated list of threads with messages for frontend summary processing.
    """
    try:
        # Order by updated_at descending (most recent first), then by created_at descending
        threads = db.query(Thread).order_by(
            Thread.updated_at.desc(),
            Thread.created_at.desc()
        ).offset(skip).limit(limit).all()

        # Return full thread data including messages for summary generation
        return [ThreadSchema.model_validate(thread) for thread in threads]

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list threads: {str(e)}"
        )


@router.get("/threads/{thread_id}", response_model=ThreadSchema)
async def get_thread(
    thread_id: int,
    db: Session = Depends(get_db)
) -> ThreadSchema:
    """
    Get a specific thread with full message history.
    """
    try:
        thread = db.query(Thread).filter(Thread.id == thread_id).first()
        
        if not thread:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Thread {thread_id} not found"
            )
            
        return ThreadSchema.model_validate(thread)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get thread: {str(e)}"
        )


# ============================================================================
# MESSAGE ENDPOINTS
# ============================================================================

@router.post("/threads/{thread_id}/messages", response_model=MessageResponse)
async def send_message(
    thread_id: int,
    message_data: MessageCreate,
    db: Session = Depends(get_db)
) -> MessageResponse:
    """
    Send a message to a thread and get agent response.

    This is the main interaction endpoint that:
    1. Validates the thread exists
    2. Saves the user's message to the database
    3. Loads the conversation history
    4. Invokes the LangGraph with the updated state
    5. Saves the agent's response to the database
    6. Returns the agent's response
    """
    print(f"🔄 API: Received message request for thread {thread_id}")
    print(f"📝 API: Message content: {message_data.content}")
    print(f"👤 API: Message author: {message_data.author}")

    try:
        # Validate thread exists
        thread = db.query(Thread).filter(Thread.id == thread_id).first()
        if not thread:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Thread {thread_id} not found"
            )
        
        # Save user message to database
        user_message = Message(
            content=message_data.content,
            author=message_data.author,
            author_type="human",
            thread_id=thread_id
        )
        
        db.add(user_message)
        db.commit()
        db.refresh(user_message)
        
        # For routing, we only need to process the new user message
        # The graph will handle the routing based on @mentions in the latest message
        new_message = HumanMessage(
            content=user_message.content,
            name=user_message.author
        )

        # Create conversation state for LangGraph with just the new message
        initial_state = ConversationState(
            messages=[new_message],
            thread_id=thread_id,
            last_agent=None,
            mentioned_agent=None
        )
        
        # Create a fresh graph instance to ensure latest configuration
        conversation_graph = create_conversation_graph()

        # Invoke the conversation graph
        result = conversation_graph.invoke(initial_state)
        
        # Extract agent response from result
        agent_messages = [
            msg for msg in result["messages"]
            if hasattr(msg, 'name') and msg.name in ["Researcher", "Analyst", "Summarizer", "Writer", "Coder", "Translator", "System"]
        ]

        print(f"🤖 API: Found {len(agent_messages)} agent messages in result")
        for i, msg in enumerate(agent_messages):
            print(f"🤖 API: Agent message {i+1}: {msg.name} - {msg.content[:100]}...")
        
        # Check if there's an @mention in the user message
        has_mention = any(agent in user_message.content for agent in ["@Researcher", "@Analyst", "@Summarizer", "@Writer", "@Coder", "@Translator"])

        if not agent_messages and has_mention:
            # Only raise error if user expected an agent response (used @mention)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="No agent response generated for @mention"
            )
        elif not agent_messages:
            # No @mention used, no agent response expected - just return success
            return MessageResponse(
                content="Message received",
                author="System",
                author_type="system",
                thread_id=thread_id
            )
        
        # Get the latest agent message
        latest_agent_message = agent_messages[-1]
        
        # Save agent response to database
        agent_response = Message(
            content=latest_agent_message.content,
            author=latest_agent_message.name or "System",
            author_type="agent",
            thread_id=thread_id
        )
        
        db.add(agent_response)
        db.commit()
        db.refresh(agent_response)

        # Notify WebSocket clients of new agent message
        try:
            await notify_new_message(thread_id, {
                "id": agent_response.id,
                "content": agent_response.content,
                "author": agent_response.author,
                "author_type": "agent",
                "created_at": agent_response.created_at.isoformat()
            })
        except Exception as e:
            print(f"WebSocket notification failed: {e}")

        # Return agent response
        return MessageResponse(
            content=agent_response.content,
            author=agent_response.author,
            author_type=agent_response.author_type,
            thread_id=thread_id
        )
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process message: {str(e)}"
        )


@router.get("/threads/{thread_id}/export")
async def export_thread(
    thread_id: int,
    format: str = "json",
    db: Session = Depends(get_db)
) -> StreamingResponse:
    """
    Export a conversation thread in various formats.

    Supported formats:
    - json: Structured JSON with full metadata
    - csv: Comma-separated values for spreadsheet import
    - markdown: Formatted markdown for documentation
    - txt: Plain text for simple reading
    """
    try:
        # Get thread with all related data
        thread = db.query(Thread).filter(Thread.id == thread_id).first()

        if not thread:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Thread {thread_id} not found"
            )

        # Export conversation
        return export_conversation(thread, format.lower())

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to export conversation: {str(e)}"
        )
