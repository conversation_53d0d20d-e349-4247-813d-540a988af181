"""
FastAPI router for the collaborative intelligence platform.

This module implements the main API endpoints:
- POST /api/threads: Create new conversation threads
- POST /api/threads/{thread_id}/messages: Handle message interactions with LangGraph
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from langchain_core.messages import HumanMessage

from models import Thread, Message
from schemas import (
    ThreadCreate, Thread as ThreadSchema, ThreadSummary,
    MessageCreate, MessageResponse
)
from graph import create_conversation_graph, ConversationState
from database import get_db

router = APIRouter(prefix="/api", tags=["threads"])


# ============================================================================
# THREAD ENDPOINTS
# ============================================================================

@router.post("/threads", response_model=ThreadSchema, status_code=status.HTTP_201_CREATED)
async def create_thread(
    thread_data: ThreadCreate,
    db: Session = Depends(get_db)
) -> ThreadSchema:
    """
    Create a new conversation thread.
    
    This endpoint creates an empty thread that can then receive messages.
    The thread will be ready for multi-agent conversations with @mention routing.
    """
    try:
        # Create new thread in database
        db_thread = Thread(
            title=thread_data.title,
            description=thread_data.description
        )
        
        db.add(db_thread)
        db.commit()
        db.refresh(db_thread)
        
        return ThreadSchema.model_validate(db_thread)
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create thread: {str(e)}"
        )


@router.get("/threads", response_model=List[ThreadSummary])
async def list_threads(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
) -> List[ThreadSummary]:
    """
    List all conversation threads with summary information.
    
    Returns a paginated list of threads without full message history
    for performance reasons.
    """
    try:
        threads = db.query(Thread).offset(skip).limit(limit).all()
        
        # Convert to summary format with message count
        thread_summaries = []
        for thread in threads:
            summary = ThreadSummary(
                id=thread.id,
                title=thread.title,
                description=thread.description,
                created_at=thread.created_at,
                updated_at=thread.updated_at,
                message_count=len(thread.messages),
                tags=thread.tags,
                participants=thread.participants
            )
            thread_summaries.append(summary)
            
        return thread_summaries
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list threads: {str(e)}"
        )


@router.get("/threads/{thread_id}", response_model=ThreadSchema)
async def get_thread(
    thread_id: int,
    db: Session = Depends(get_db)
) -> ThreadSchema:
    """
    Get a specific thread with full message history.
    """
    try:
        thread = db.query(Thread).filter(Thread.id == thread_id).first()
        
        if not thread:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Thread {thread_id} not found"
            )
            
        return ThreadSchema.model_validate(thread)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get thread: {str(e)}"
        )


# ============================================================================
# MESSAGE ENDPOINTS
# ============================================================================

@router.post("/threads/{thread_id}/messages", response_model=MessageResponse)
async def send_message(
    thread_id: int,
    message_data: MessageCreate,
    db: Session = Depends(get_db)
) -> MessageResponse:
    """
    Send a message to a thread and get agent response.
    
    This is the main interaction endpoint that:
    1. Validates the thread exists
    2. Saves the user's message to the database
    3. Loads the conversation history
    4. Invokes the LangGraph with the updated state
    5. Saves the agent's response to the database
    6. Returns the agent's response
    """
    try:
        # Validate thread exists
        thread = db.query(Thread).filter(Thread.id == thread_id).first()
        if not thread:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Thread {thread_id} not found"
            )
        
        # Save user message to database
        user_message = Message(
            content=message_data.content,
            author=message_data.author,
            author_type="human",
            thread_id=thread_id
        )
        
        db.add(user_message)
        db.commit()
        db.refresh(user_message)
        
        # Load conversation history from database
        messages = db.query(Message).filter(
            Message.thread_id == thread_id
        ).order_by(Message.created_at).all()
        
        # Convert database messages to LangChain message format
        langchain_messages = []
        for msg in messages:
            if msg.author_type == "human":
                langchain_messages.append(HumanMessage(
                    content=msg.content,
                    name=msg.author
                ))
            else:
                # Agent messages - we'll use AIMessage but this gets handled by add_messages
                from langchain_core.messages import AIMessage
                langchain_messages.append(AIMessage(
                    content=msg.content,
                    name=msg.author
                ))
        
        # Create conversation state for LangGraph
        initial_state = ConversationState(
            messages=langchain_messages,
            thread_id=thread_id,
            last_agent=None,
            mentioned_agent=None
        )
        
        # Create a fresh graph instance to ensure latest configuration
        conversation_graph = create_conversation_graph()

        # Debug: Print the latest message content
        print(f"🔍 DEBUG: Processing message: '{message_data.content}'")
        print(f"🔍 DEBUG: Graph nodes: {list(conversation_graph.get_graph().nodes.keys())}")

        # Invoke the conversation graph
        result = conversation_graph.invoke(initial_state)

        print(f"🔍 DEBUG: Graph result keys: {result.keys()}")
        print(f"🔍 DEBUG: Last agent: {result.get('last_agent', 'None')}")
        
        # Extract agent response from result
        agent_messages = [
            msg for msg in result["messages"] 
            if hasattr(msg, 'name') and msg.name in ["Researcher", "Analyst", "Summarizer", "System"]
        ]
        
        if not agent_messages:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="No agent response generated"
            )
        
        # Get the latest agent message
        latest_agent_message = agent_messages[-1]
        
        # Save agent response to database
        agent_response = Message(
            content=latest_agent_message.content,
            author=latest_agent_message.name or "System",
            author_type="agent",
            thread_id=thread_id
        )
        
        db.add(agent_response)
        db.commit()
        db.refresh(agent_response)
        
        # Return agent response
        return MessageResponse(
            content=agent_response.content,
            author=agent_response.author,
            author_type=agent_response.author_type,
            thread_id=thread_id
        )
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process message: {str(e)}"
        )
