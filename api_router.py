"""
FastAPI router for the collaborative intelligence platform.

This module implements the main API endpoints:
- POST /api/threads: Create new conversation threads
- POST /api/threads/{thread_id}/messages: Handle message interactions with LangGraph
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from langchain_core.messages import HumanMessage
import json
import asyncio

from models import Thread, Message
from schemas import (
    ThreadCreate, Thread as ThreadSchema,
    MessageCreate, MessageResponse
)
from graph import create_conversation_graph, ConversationState
from database import get_db
from export_service import export_conversation
from websocket_manager import notify_new_message, notify_agent_thinking

router = APIRouter(prefix="/api", tags=["threads"])


# ============================================================================
# THREAD ENDPOINTS
# ============================================================================

@router.post("/threads", response_model=ThreadSchema, status_code=status.HTTP_201_CREATED)
async def create_thread(
    thread_data: ThreadCreate,
    db: Session = Depends(get_db)
) -> ThreadSchema:
    """
    Create a new conversation thread.
    
    This endpoint creates an empty thread that can then receive messages.
    The thread will be ready for multi-agent conversations with @mention routing.
    """
    try:
        # Create new thread in database
        db_thread = Thread(
            title=thread_data.title,
            description=thread_data.description
        )
        
        db.add(db_thread)
        db.commit()
        db.refresh(db_thread)
        
        return ThreadSchema.model_validate(db_thread)
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create thread: {str(e)}"
        )


@router.get("/threads", response_model=List[ThreadSchema])
async def list_threads(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
) -> List[ThreadSchema]:
    """
    List all conversation threads with full message history for summary generation.

    Returns a paginated list of threads with messages for frontend summary processing.
    """
    try:
        # Order by updated_at descending (most recent first), then by created_at descending
        threads = db.query(Thread).order_by(
            Thread.updated_at.desc(),
            Thread.created_at.desc()
        ).offset(skip).limit(limit).all()

        # Return full thread data including messages for summary generation
        return [ThreadSchema.model_validate(thread) for thread in threads]

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list threads: {str(e)}"
        )


@router.get("/threads/{thread_id}", response_model=ThreadSchema)
async def get_thread(
    thread_id: int,
    db: Session = Depends(get_db)
) -> ThreadSchema:
    """
    Get a specific thread with full message history.
    """
    try:
        thread = db.query(Thread).filter(Thread.id == thread_id).first()
        
        if not thread:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Thread {thread_id} not found"
            )
            
        return ThreadSchema.model_validate(thread)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get thread: {str(e)}"
        )


# ============================================================================
# MESSAGE ENDPOINTS
# ============================================================================

@router.post("/threads/{thread_id}/messages", response_model=MessageResponse)
async def send_message(
    thread_id: int,
    message_data: MessageCreate,
    db: Session = Depends(get_db)
) -> MessageResponse:
    """
    Send a message to a thread and get agent response.
    
    This is the main interaction endpoint that:
    1. Validates the thread exists
    2. Saves the user's message to the database
    3. Loads the conversation history
    4. Invokes the LangGraph with the updated state
    5. Saves the agent's response to the database
    6. Returns the agent's response
    """
    try:
        # Validate thread exists
        thread = db.query(Thread).filter(Thread.id == thread_id).first()
        if not thread:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Thread {thread_id} not found"
            )
        
        # Save user message to database
        user_message = Message(
            content=message_data.content,
            author=message_data.author,
            author_type="human",
            thread_id=thread_id
        )
        
        db.add(user_message)
        db.commit()
        db.refresh(user_message)
        
        # For routing, we only need to process the new user message
        # The graph will handle the routing based on @mentions in the latest message
        new_message = HumanMessage(
            content=user_message.content,
            name=user_message.author
        )

        # Create conversation state for LangGraph with just the new message
        initial_state = ConversationState(
            messages=[new_message],
            thread_id=thread_id,
            last_agent=None,
            mentioned_agent=None
        )
        
        # Create a fresh graph instance to ensure latest configuration
        conversation_graph = create_conversation_graph()

        # Invoke the conversation graph
        result = conversation_graph.invoke(initial_state)
        
        # Extract agent response from result
        agent_messages = [
            msg for msg in result["messages"]
            if hasattr(msg, 'name') and msg.name in ["Researcher", "Analyst", "Summarizer", "Writer", "Coder", "Translator", "System"]
        ]
        
        # Check if there's an @mention in the user message
        has_mention = any(agent in user_message.content for agent in ["@Researcher", "@Analyst", "@Summarizer", "@Writer", "@Coder", "@Translator"])

        if not agent_messages and has_mention:
            # Only raise error if user expected an agent response (used @mention)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="No agent response generated for @mention"
            )
        elif not agent_messages:
            # No @mention used, no agent response expected - just return success
            return MessageResponse(
                content="Message received",
                author="System",
                author_type="system",
                thread_id=thread_id
            )
        
        # Get the latest agent message
        latest_agent_message = agent_messages[-1]
        
        # Save agent response to database
        agent_response = Message(
            content=latest_agent_message.content,
            author=latest_agent_message.name or "System",
            author_type="agent",
            thread_id=thread_id
        )
        
        db.add(agent_response)
        db.commit()
        db.refresh(agent_response)

        # Notify WebSocket clients of new agent message
        try:
            await notify_new_message(thread_id, {
                "id": agent_response.id,
                "content": agent_response.content,
                "author": agent_response.author,
                "author_type": "agent",
                "created_at": agent_response.created_at.isoformat()
            })
        except Exception as e:
            print(f"WebSocket notification failed: {e}")

        # Return agent response
        return MessageResponse(
            content=agent_response.content,
            author=agent_response.author,
            author_type=agent_response.author_type,
            thread_id=thread_id
        )
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process message: {str(e)}"
        )


@router.post("/threads/{thread_id}/messages/stream")
async def send_message_stream(
    thread_id: int,
    message_data: MessageCreate,
    db: Session = Depends(get_db)
):
    """
    Send a message to a thread and get streaming agent response.

    This endpoint provides real-time streaming of agent responses using Server-Sent Events.
    """
    from fastapi.responses import StreamingResponse
    from graph import create_azure_llm, parse_mention
    from langchain_core.messages import HumanMessage, SystemMessage
    import re

    try:
        # Validate thread exists
        thread = db.query(Thread).filter(Thread.id == thread_id).first()
        if not thread:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Thread {thread_id} not found"
            )

        # Save user message to database
        user_message = Message(
            content=message_data.content,
            author=message_data.author,
            author_type="human",
            thread_id=thread_id
        )

        db.add(user_message)
        db.commit()
        db.refresh(user_message)

        # Check if there's an @mention in the user message
        has_mention = any(agent in user_message.content for agent in ["@Researcher", "@Analyst", "@Summarizer", "@Writer", "@Coder", "@Translator"])

        if not has_mention:
            # No @mention used, return simple response
            async def simple_response():
                yield f"data: {json.dumps({'type': 'message', 'content': 'Message received', 'author': 'System', 'done': True})}\n\n"

            return StreamingResponse(simple_response(), media_type="text/plain")

        # Parse the mentioned agent
        mentioned_agent = parse_mention(user_message.content)
        if not mentioned_agent:
            async def error_response():
                yield f"data: {json.dumps({'type': 'error', 'content': 'No valid agent mentioned', 'done': True})}\n\n"

            return StreamingResponse(error_response(), media_type="text/plain")

        # Create streaming response
        async def generate_stream():
            try:
                # Create Azure LLM instance
                azure_llm = create_azure_llm()

                # Extract the user's query (remove @mention)
                query = re.sub(r'@\w+\s*', '', user_message.content).strip()

                # Create appropriate prompts based on agent type
                if mentioned_agent == "researcher":
                    system_prompt = SystemMessage(content="You are a Research Agent specializing in finding and synthesizing information. Be factual, cite your sources, and present information clearly.")
                    human_prompt = HumanMessage(content=f"User Query: {query}\n\nPlease provide a comprehensive research-based response.")
                elif mentioned_agent == "analyst":
                    system_prompt = SystemMessage(content="You are an Analysis Agent specializing in synthesizing information and providing insights. Be analytical, insightful, and focus on the bigger picture.")
                    human_prompt = HumanMessage(content=f"Analysis Request: {query}\n\nPlease provide a thorough analysis with insights and recommendations.")
                elif mentioned_agent == "coder":
                    system_prompt = SystemMessage(content="You are a Coder Agent specializing in programming and software development. Always provide clean, well-commented code with explanations.")
                    human_prompt = HumanMessage(content=f"Coding Request: {query}\n\nPlease provide a comprehensive technical solution with code examples and explanations.")
                else:
                    # Default system prompt for other agents
                    system_prompt = SystemMessage(content=f"You are a {mentioned_agent.title()} Agent. Provide helpful, accurate, and detailed responses.")
                    human_prompt = HumanMessage(content=f"Request: {query}")

                # Stream the response
                full_response = ""
                agent_name = mentioned_agent.title()

                yield f"data: {json.dumps({'type': 'start', 'agent': agent_name})}\n\n"

                for chunk in azure_llm.stream([system_prompt, human_prompt]):
                    if hasattr(chunk, 'content') and chunk.content:
                        full_response += chunk.content
                        yield f"data: {json.dumps({'type': 'chunk', 'content': chunk.content, 'agent': agent_name})}\n\n"

                # Save the complete response to database
                agent_response = Message(
                    content=full_response,
                    author=agent_name,
                    author_type="agent",
                    thread_id=thread_id
                )

                db.add(agent_response)
                db.commit()
                db.refresh(agent_response)

                # Send completion signal
                yield f"data: {json.dumps({'type': 'complete', 'agent': agent_name, 'message_id': agent_response.id, 'done': True})}\n\n"

                # Notify WebSocket clients of the complete message
                try:
                    await notify_new_message(thread_id, {
                        "id": agent_response.id,
                        "content": agent_response.content,
                        "author": agent_response.author,
                        "author_type": "agent",
                        "created_at": agent_response.created_at.isoformat()
                    })
                except Exception as e:
                    print(f"WebSocket notification failed: {e}")

            except Exception as e:
                yield f"data: {json.dumps({'type': 'error', 'content': str(e), 'done': True})}\n\n"

        return StreamingResponse(generate_stream(), media_type="text/plain")

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process streaming message: {str(e)}"
        )


@router.get("/threads/{thread_id}/export")
async def export_thread(
    thread_id: int,
    format: str = "json",
    db: Session = Depends(get_db)
) -> StreamingResponse:
    """
    Export a conversation thread in various formats.

    Supported formats:
    - json: Structured JSON with full metadata
    - csv: Comma-separated values for spreadsheet import
    - markdown: Formatted markdown for documentation
    - txt: Plain text for simple reading
    """
    try:
        # Get thread with all related data
        thread = db.query(Thread).filter(Thread.id == thread_id).first()

        if not thread:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Thread {thread_id} not found"
            )

        # Export conversation
        return export_conversation(thread, format.lower())

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to export conversation: {str(e)}"
        )
