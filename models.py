"""
SQLAlchemy models for the collaborative intelligence platform.
"""
from datetime import datetime
from typing import List, Optional
from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String, Text, DateTime, ForeignKey, Table
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.sql import func

Base = declarative_base()

# Association table for many-to-many relationship between threads and tags
thread_tags = Table(
    'thread_tags',
    Base.metadata,
    Column('thread_id', Integer, ForeignKey('threads.id'), primary_key=True),
    Column('tag_id', Integer, ForeignKey('tags.id'), primary_key=True)
)

# Association table for many-to-many relationship between threads and users (participants)
thread_participants = Table(
    'thread_participants',
    Base.metadata,
    Column('thread_id', Integer, ForeignKey('threads.id'), primary_key=True),
    Column('user_id', Integer, ForeignKey('users.id'), primary_key=True)
)


class User(Base):
    """User model for human participants."""
    __tablename__ = 'users'
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    username: Mapped[str] = mapped_column(String(50), unique=True, index=True, nullable=False)
    email: Mapped[Optional[str]] = mapped_column(String(100), unique=True, index=True)
    full_name: Mapped[Optional[str]] = mapped_column(String(100))
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    messages: Mapped[List["Message"]] = relationship("Message", back_populates="user")
    participated_threads: Mapped[List["Thread"]] = relationship(
        "Thread", secondary=thread_participants, back_populates="participants"
    )


class Thread(Base):
    """Thread model for persistent conversations."""
    __tablename__ = 'threads'
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    title: Mapped[str] = mapped_column(String(200), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
    )
    
    # Relationships
    messages: Mapped[List["Message"]] = relationship(
        "Message", back_populates="thread", cascade="all, delete-orphan", order_by="Message.created_at"
    )
    tags: Mapped[List["Tag"]] = relationship(
        "Tag", secondary=thread_tags, back_populates="threads"
    )
    participants: Mapped[List["User"]] = relationship(
        "User", secondary=thread_participants, back_populates="participated_threads"
    )


class Message(Base):
    """Message model for individual messages in threads."""
    __tablename__ = 'messages'
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    content: Mapped[str] = mapped_column(Text, nullable=False)
    author: Mapped[str] = mapped_column(String(100), nullable=False)  # e.g., "User", "Researcher", "Analyst"
    author_type: Mapped[str] = mapped_column(String(20), nullable=False, default="human")  # "human" or "agent"
    thread_id: Mapped[int] = mapped_column(Integer, ForeignKey('threads.id'), nullable=False)
    user_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey('users.id'))  # Only for human messages
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    thread: Mapped["Thread"] = relationship("Thread", back_populates="messages")
    user: Mapped[Optional["User"]] = relationship("User", back_populates="messages")


class Tag(Base):
    """Tag model for categorizing threads."""
    __tablename__ = 'tags'
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String(50), unique=True, nullable=False, index=True)
    description: Mapped[Optional[str]] = mapped_column(Text)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    threads: Mapped[List["Thread"]] = relationship(
        "Thread", secondary=thread_tags, back_populates="tags"
    )
