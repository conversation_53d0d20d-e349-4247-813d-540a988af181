"""
Comprehensive test script for multi-user collaboration features.
Tests multiple users joining the same thread, chatting with each other, and using AI agents.
"""
import requests
import time
import json
import asyncio
import websockets
import threading
from concurrent.futures import ThreadPoolExecutor

BASE_URL = "http://localhost:8000"
WS_BASE_URL = "ws://localhost:8000"

class MultiUserTester:
    def __init__(self):
        self.users = []
        self.threads = []
        self.websockets = {}
        
    def setup_demo_users(self):
        """Create and authenticate demo users."""
        demo_users = [
            {"username": "alice", "display_name": "<PERSON>"},
            {"username": "bob", "display_name": "<PERSON>"},
            {"username": "carol", "display_name": "<PERSON>"},
            {"username": "david", "display_name": "<PERSON>"}
        ]
        
        print("🔐 Setting up demo users...")
        
        for user_data in demo_users:
            # Register user (if not exists)
            register_response = requests.post(f"{BASE_URL}/api/auth/register", json={
                "username": user_data["username"],
                "email": f"{user_data['username']}@demo.com",
                "full_name": user_data["display_name"]
            })
            
            # Login user
            login_response = requests.post(f"{BASE_URL}/api/auth/login", json={
                "username": user_data["username"],
                "password": "demo123"
            })
            
            if login_response.status_code == 200:
                login_data = login_response.json()
                user_info = {
                    "username": user_data["username"],
                    "display_name": user_data["display_name"],
                    "token": login_data["access_token"],
                    "user_data": login_data["user"]
                }
                self.users.append(user_info)
                print(f"   ✅ {user_info['display_name']} authenticated")
            else:
                print(f"   ❌ Failed to authenticate {user_data['username']}")
        
        print(f"✅ {len(self.users)} users ready for collaboration testing")
        return self.users
    
    def create_collaborative_thread(self):
        """Create a thread for multi-user collaboration."""
        print("\n📝 Creating collaborative thread...")
        
        # Use first user to create the thread
        if not self.users:
            print("❌ No users available")
            return None
            
        creator = self.users[0]
        
        thread_data = {
            "title": "Multi-User Collaboration Test",
            "description": "Testing real-time collaboration between multiple users and AI agents"
        }
        
        headers = {"Authorization": f"Bearer {creator['token']}"}
        response = requests.post(f"{BASE_URL}/api/threads", json=thread_data, headers=headers)
        
        if response.status_code == 201:
            thread = response.json()
            self.threads.append(thread)
            print(f"✅ Thread created: '{thread['title']}' (ID: {thread['id']})")
            return thread
        else:
            print(f"❌ Failed to create thread: {response.status_code}")
            return None
    
    def simulate_user_conversation(self, thread_id):
        """Simulate a realistic multi-user conversation with AI agents."""
        print(f"\n💬 Starting multi-user conversation in thread {thread_id}")
        
        conversation_flow = [
            # Alice starts the conversation
            {
                "user": "alice",
                "message": "Hi everyone! Let's discuss the new project requirements.",
                "delay": 1
            },
            # Bob joins
            {
                "user": "bob", 
                "message": "Hey Alice! I'm here. What are the main objectives?",
                "delay": 2
            },
            # Alice asks AI for research
            {
                "user": "alice",
                "message": "@Researcher Can you help us find information about market trends in renewable energy?",
                "delay": 3
            },
            # Carol joins and asks for analysis
            {
                "user": "carol",
                "message": "Great idea! @Analyst Once we have the research, can you analyze the competitive landscape?",
                "delay": 4
            },
            # David joins with a different request
            {
                "user": "david",
                "message": "@Writer Can you help us draft a project proposal outline based on the research and analysis?",
                "delay": 2
            },
            # Bob asks for translation
            {
                "user": "bob",
                "message": "@Translator We might need this proposal in Spanish and French for our international partners.",
                "delay": 3
            },
            # Alice summarizes
            {
                "user": "alice",
                "message": "@Summarizer Can you provide a summary of our discussion and action items so far?",
                "delay": 2
            }
        ]
        
        for step in conversation_flow:
            user = next((u for u in self.users if u["username"] == step["user"]), None)
            if not user:
                print(f"❌ User {step['user']} not found")
                continue
                
            print(f"💬 {user['display_name']}: {step['message']}")
            
            # Send message
            headers = {"Authorization": f"Bearer {user['token']}"}
            message_data = {
                "content": step["message"],
                "author": user["username"]
            }
            
            response = requests.post(
                f"{BASE_URL}/api/threads/{thread_id}/messages", 
                json=message_data, 
                headers=headers
            )
            
            if response.status_code == 200:
                print(f"   ✅ Message sent successfully")
                
                # Wait for AI response if @mention was used
                if "@" in step["message"]:
                    print(f"   🤖 Waiting for AI agent response...")
                    time.sleep(step["delay"] + 2)  # Extra time for AI processing
                else:
                    time.sleep(step["delay"])
            else:
                print(f"   ❌ Failed to send message: {response.status_code}")
            
            time.sleep(1)  # Brief pause between messages
    
    def test_real_time_features(self, thread_id):
        """Test real-time features like user presence and typing indicators."""
        print(f"\n⚡ Testing real-time features for thread {thread_id}")
        
        # This would require WebSocket testing which is more complex
        # For now, we'll simulate the behavior
        
        print("🔗 Simulating WebSocket connections...")
        for user in self.users[:3]:  # Test with first 3 users
            print(f"   📡 {user['display_name']} connected to WebSocket")
            
        print("👥 Simulating user presence indicators...")
        print("⌨️  Simulating typing indicators...")
        print("📨 Simulating real-time message delivery...")
        
        # In a real implementation, this would:
        # 1. Connect multiple WebSocket clients
        # 2. Send typing indicators
        # 3. Verify real-time message delivery
        # 4. Test user join/leave notifications
        
        time.sleep(2)
        print("✅ Real-time features simulation completed")
    
    def verify_thread_state(self, thread_id):
        """Verify the final state of the thread after collaboration."""
        print(f"\n🔍 Verifying thread state for thread {thread_id}")
        
        # Get thread with all messages
        response = requests.get(f"{BASE_URL}/api/threads/{thread_id}")
        
        if response.status_code == 200:
            thread = response.json()
            messages = thread.get("messages", [])
            
            print(f"📊 Thread Analysis:")
            print(f"   📝 Total messages: {len(messages)}")
            
            # Count messages by type
            user_messages = [msg for msg in messages if msg.get("author_type") == "human"]
            agent_messages = [msg for msg in messages if msg.get("author_type") == "agent"]
            
            print(f"   👥 User messages: {len(user_messages)}")
            print(f"   🤖 Agent messages: {len(agent_messages)}")
            
            # Count unique users
            unique_users = set(msg.get("author") for msg in user_messages)
            print(f"   👤 Unique users participated: {len(unique_users)}")
            print(f"   📋 Users: {', '.join(unique_users)}")
            
            # Count unique agents
            unique_agents = set(msg.get("author") for msg in agent_messages)
            print(f"   🤖 AI agents involved: {len(unique_agents)}")
            print(f"   🎯 Agents: {', '.join(unique_agents)}")
            
            # Show recent messages
            print(f"\n📜 Recent conversation:")
            for msg in messages[-5:]:  # Last 5 messages
                author_type = "👤" if msg.get("author_type") == "human" else "🤖"
                content_preview = msg["content"][:60] + "..." if len(msg["content"]) > 60 else msg["content"]
                print(f"   {author_type} {msg['author']}: {content_preview}")
            
            return True
        else:
            print(f"❌ Failed to get thread: {response.status_code}")
            return False
    
    def run_comprehensive_test(self):
        """Run the complete multi-user collaboration test suite."""
        print("🧪 Multi-User Collaboration Test Suite")
        print("=" * 50)
        
        try:
            # Setup users
            users = self.setup_demo_users()
            if len(users) < 2:
                print("❌ Need at least 2 users for collaboration testing")
                return False
            
            # Create collaborative thread
            thread = self.create_collaborative_thread()
            if not thread:
                print("❌ Failed to create thread")
                return False
            
            thread_id = thread["id"]
            
            # Simulate multi-user conversation
            self.simulate_user_conversation(thread_id)
            
            # Test real-time features
            self.test_real_time_features(thread_id)
            
            # Verify final state
            success = self.verify_thread_state(thread_id)
            
            if success:
                print("\n" + "=" * 50)
                print("🎉 Multi-User Collaboration Test PASSED!")
                print("\n✅ Features Tested:")
                print("   • Multi-user authentication")
                print("   • Collaborative thread creation")
                print("   • User-to-user messaging")
                print("   • AI agent interactions (@mentions)")
                print("   • Real-time communication simulation")
                print("   • Thread state verification")
                
                print(f"\n🌐 Test Results Available At:")
                print(f"   Frontend: http://localhost:3000/thread/{thread_id}")
                print(f"   API: {BASE_URL}/api/threads/{thread_id}")
                
                return True
            else:
                print("\n❌ Multi-User Collaboration Test FAILED!")
                return False
                
        except Exception as e:
            print(f"\n💥 Test failed with error: {str(e)}")
            return False

def main():
    """Run the multi-user collaboration tests."""
    tester = MultiUserTester()
    
    try:
        success = tester.run_comprehensive_test()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        exit(1)
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Make sure the server is running at http://localhost:8000")
        exit(1)

if __name__ == "__main__":
    main()
