import React, { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { Brain, MessageSquare, Settings, Activity, LogIn } from 'lucide-react'
import { useThread } from '../context/ThreadContext'
import { useAuth } from '../context/AuthContext'
import UserMenu from './UserMenu'
import LoginModal from './LoginModal'

function Layout({ children }) {
  const location = useLocation()
  const { healthStatus } = useThread()
  const { user, isAuthenticated } = useAuth()
  const [showLoginModal, setShowLoginModal] = useState(false)

  const isHealthy = healthStatus?.status === 'healthy'

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo and Title */}
            <div className="flex items-center space-x-3">
              <div className="flex items-center justify-center w-10 h-10 bg-primary-600 rounded-lg">
                <Brain className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  Collaborative Intelligence
                </h1>
                <p className="text-sm text-gray-500">Multi-Agent AI Platform</p>
              </div>
            </div>

            {/* Navigation */}
            <nav className="flex items-center space-x-6">
              <Link
                to="/"
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  location.pathname === '/'
                    ? 'bg-primary-100 text-primary-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                <MessageSquare className="w-4 h-4" />
                <span>Conversations</span>
              </Link>

              {/* Health Status */}
              <div className="flex items-center space-x-2">
                <Activity className={`w-4 h-4 ${isHealthy ? 'text-green-500' : 'text-red-500'}`} />
                <span className={`text-sm font-medium ${isHealthy ? 'text-green-700' : 'text-red-700'}`}>
                  {isHealthy ? 'Online' : 'Offline'}
                </span>
              </div>

              {/* Agent Count */}
              {healthStatus?.agents_available && (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <span className="font-medium">{healthStatus.agents_available.length}</span>
                  <span>Agents</span>
                </div>
              )}

              {/* Authentication */}
              {isAuthenticated ? (
                <UserMenu />
              ) : (
                <button
                  onClick={() => setShowLoginModal(true)}
                  className="flex items-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                >
                  <LogIn className="w-4 h-4" />
                  <span>Sign In</span>
                </button>
              )}
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {children}
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-auto">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              Powered by LangGraph, Azure OpenAI, and FastAPI
            </div>
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <span>Available Agents:</span>
              <div className="flex space-x-2">
                {['Researcher', 'Analyst', 'Summarizer', 'Writer', 'Coder', 'Translator'].map((agent) => (
                  <span
                    key={agent}
                    className={`agent-badge agent-${agent.toLowerCase()}`}
                  >
                    @{agent}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      </footer>

      {/* Login Modal */}
      <LoginModal
        isOpen={showLoginModal}
        onClose={() => setShowLoginModal(false)}
      />
    </div>
  )
}

export default Layout
