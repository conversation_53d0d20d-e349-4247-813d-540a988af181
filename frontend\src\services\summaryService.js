/**
 * Service for generating thread summaries
 */

// Cache for generated summaries to avoid regenerating
const summaryCache = new Map()

/**
 * Generate a smart summary for a thread based on its content
 */
export function generateThreadSummary(thread) {
  // Check cache first
  const cacheKey = `${thread.id}-${thread.updated_at}`
  if (summaryCache.has(cacheKey)) {
    return summaryCache.get(cacheKey)
  }

  let summary = generateLocalSummary(thread)
  
  // Cache the result
  summaryCache.set(cacheKey, summary)
  
  return summary
}

/**
 * Generate summary using local analysis
 */
function generateLocalSummary(thread) {
  if (!thread.messages || thread.messages.length === 0) {
    // Use thread description if available, otherwise generic message
    if (thread.description && thread.description.trim()) {
      return thread.description.length > 80
        ? thread.description.substring(0, 80) + '...'
        : thread.description
    }
    return "New conversation - no messages yet"
  }

  // Get key messages for analysis
  const userMessages = thread.messages.filter(msg => 
    msg.author === 'User' || msg.author === 'TestUser'
  )
  
  const agentMessages = thread.messages.filter(msg => 
    msg.author !== 'User' && msg.author !== 'TestUser'
  )

  // Analyze conversation patterns
  const analysis = analyzeConversationContent(userMessages, agentMessages)
  
  return analysis.summary
}

/**
 * Analyze conversation content to generate insights
 */
function analyzeConversationContent(userMessages, agentMessages) {
  const allText = [...userMessages, ...agentMessages]
    .map(msg => msg.content.toLowerCase())
    .join(' ')

  // Extract mentioned agents
  const mentionedAgents = extractMentionedAgents(userMessages)

  // Topic detection based on keywords
  const topics = detectTopics(allText)

  // Extract conversation purpose from user messages
  const conversationPurpose = extractConversationPurpose(userMessages)

  // Generate summary based on analysis
  let summary = generateSummaryFromTopics(topics, mentionedAgents, userMessages, conversationPurpose)

  return {
    summary,
    topics,
    mentionedAgents,
    conversationPurpose,
    messageCount: userMessages.length + agentMessages.length
  }
}

/**
 * Extract the main purpose/intent from user messages
 */
function extractConversationPurpose(userMessages) {
  if (userMessages.length === 0) return null

  // Look at the first few user messages to understand intent
  const firstMessages = userMessages.slice(0, 3)
  const combinedText = firstMessages.map(msg => msg.content.toLowerCase()).join(' ')

  // Remove @mentions for cleaner analysis
  const cleanText = combinedText.replace(/@\w+\s*/g, '').trim()

  // Extract key intent patterns
  if (cleanText.includes('translate') || cleanText.includes('translation')) {
    return 'Translation request'
  } else if (cleanText.includes('analyze') || cleanText.includes('analysis')) {
    return 'Analysis request'
  } else if (cleanText.includes('research') || cleanText.includes('find') || cleanText.includes('search')) {
    return 'Research inquiry'
  } else if (cleanText.includes('test') || cleanText.includes('testing')) {
    return 'Testing session'
  } else if (cleanText.includes('write') || cleanText.includes('create') || cleanText.includes('draft')) {
    return 'Content creation'
  } else if (cleanText.includes('help') || cleanText.includes('how to') || cleanText.includes('explain')) {
    return 'Help request'
  } else if (cleanText.includes('code') || cleanText.includes('programming') || cleanText.includes('debug')) {
    return 'Programming assistance'
  } else if (cleanText.includes('summarize') || cleanText.includes('summary')) {
    return 'Summarization request'
  }

  return null
}

/**
 * Extract which agents were mentioned in the conversation
 */
function extractMentionedAgents(userMessages) {
  const agents = new Set()
  const agentPattern = /@(Researcher|Analyst|Summarizer|Writer|Coder|Translator)/gi
  
  userMessages.forEach(msg => {
    const matches = msg.content.match(agentPattern)
    if (matches) {
      matches.forEach(match => {
        agents.add(match.substring(1)) // Remove @ symbol
      })
    }
  })
  
  return Array.from(agents)
}

/**
 * Detect conversation topics based on keywords
 */
function detectTopics(text) {
  const topicKeywords = {
    'Trade & Economics': ['tariff', 'trade', 'economic', 'economy', 'market', 'business', 'finance', 'revenue', 'cost'],
    'Technology & Programming': ['code', 'programming', 'software', 'development', 'api', 'database', 'algorithm', 'tech'],
    'Research & Analysis': ['research', 'analysis', 'data', 'study', 'investigate', 'findings', 'insights', 'trends'],
    'Content & Writing': ['write', 'content', 'article', 'blog', 'document', 'text', 'writing', 'draft'],
    'File Management': ['file', 'upload', 'document', 'pdf', 'download', 'attachment', 'export'],
    'Testing & QA': ['test', 'testing', 'functionality', 'feature', 'bug', 'quality', 'validation'],
    'Translation & Language': ['translate', 'translation', 'language', 'multilingual', 'localization'],
    'Strategy & Planning': ['strategy', 'plan', 'planning', 'roadmap', 'goals', 'objectives', 'recommendations']
  }

  const detectedTopics = []
  
  for (const [topic, keywords] of Object.entries(topicKeywords)) {
    const matchCount = keywords.filter(keyword => text.includes(keyword)).length
    if (matchCount >= 2) { // Require at least 2 keyword matches
      detectedTopics.push({
        topic,
        relevance: matchCount / keywords.length,
        matchedKeywords: keywords.filter(keyword => text.includes(keyword))
      })
    }
  }
  
  // Sort by relevance
  return detectedTopics.sort((a, b) => b.relevance - a.relevance)
}

/**
 * Generate summary text based on detected topics and agents
 */
function generateSummaryFromTopics(topics, mentionedAgents, userMessages, conversationPurpose = null) {
  // Use conversation purpose if available and no clear topics
  if (conversationPurpose && topics.length === 0) {
    return conversationPurpose
  }

  // If no clear topics detected, create summary from user intent
  if (topics.length === 0) {
    if (userMessages.length > 0) {
      // Extract the core intent from the first user message
      const firstMessage = userMessages[0].content
        .replace(/@\w+\s*/g, '') // Remove @mentions
        .trim()

      // Create a concise summary of user intent
      if (firstMessage.toLowerCase().includes('test')) {
        return 'Testing platform features and functionality'
      } else if (firstMessage.toLowerCase().includes('help') || firstMessage.toLowerCase().includes('how')) {
        return 'Seeking assistance and guidance'
      } else if (firstMessage.toLowerCase().includes('analyze') || firstMessage.toLowerCase().includes('analysis')) {
        return 'Requesting analysis and insights'
      } else if (firstMessage.toLowerCase().includes('create') || firstMessage.toLowerCase().includes('write')) {
        return 'Content creation and writing tasks'
      } else if (firstMessage.toLowerCase().includes('translate')) {
        return 'Translation and language services'
      } else {
        // Use a very short version of the first message
        const shortMessage = firstMessage.length > 50 ? firstMessage.substring(0, 50) + '...' : firstMessage
        return shortMessage || 'General conversation'
      }
    }
    return 'New conversation'
  }

  const primaryTopic = topics[0]
  let summary = ''

  // Create concise, descriptive summaries
  switch (primaryTopic.topic) {
    case 'Trade & Economics':
      summary = 'Economic analysis and trade policy discussion'
      break
    case 'Technology & Programming':
      summary = 'Technical development and programming assistance'
      break
    case 'Research & Analysis':
      summary = 'Research and data analysis session'
      break
    case 'Content & Writing':
      summary = 'Content creation and writing collaboration'
      break
    case 'File Management':
      summary = 'Document processing and file analysis'
      break
    case 'Testing & QA':
      summary = 'Platform testing and quality assurance'
      break
    case 'Translation & Language':
      summary = 'Translation and multilingual support'
      break
    case 'Strategy & Planning':
      summary = 'Strategic planning and decision making'
      break
    default:
      summary = `${primaryTopic.topic} discussion`
  }

  // Add agent collaboration info more concisely
  if (mentionedAgents.length > 0) {
    if (mentionedAgents.length === 1) {
      summary += ` with ${mentionedAgents[0]}`
    } else if (mentionedAgents.length <= 3) {
      summary += ` with ${mentionedAgents.join(', ')}`
    } else {
      summary += ` with multiple AI agents`
    }
  }

  return summary
}

/**
 * Get conversation statistics
 */
export function getConversationStats(thread) {
  if (!thread.messages || thread.messages.length === 0) {
    return {
      totalMessages: 0,
      userMessages: 0,
      agentMessages: 0,
      uniqueAgents: 0,
      topics: []
    }
  }

  const userMessages = thread.messages.filter(msg => 
    msg.author === 'User' || msg.author === 'TestUser'
  )
  
  const agentMessages = thread.messages.filter(msg => 
    msg.author !== 'User' && msg.author !== 'TestUser'
  )

  const uniqueAgents = new Set(agentMessages.map(msg => msg.author))
  
  const allText = thread.messages.map(msg => msg.content.toLowerCase()).join(' ')
  const topics = detectTopics(allText)

  return {
    totalMessages: thread.messages.length,
    userMessages: userMessages.length,
    agentMessages: agentMessages.length,
    uniqueAgents: uniqueAgents.size,
    topics: topics.slice(0, 3) // Top 3 topics
  }
}

/**
 * Clear summary cache (useful when thread is updated)
 */
export function clearSummaryCache(threadId = null) {
  if (threadId) {
    // Clear cache for specific thread
    for (const key of summaryCache.keys()) {
      if (key.startsWith(`${threadId}-`)) {
        summaryCache.delete(key)
      }
    }
  } else {
    // Clear entire cache
    summaryCache.clear()
  }
}
