/**
 * Service for generating thread summaries
 */

// Cache for generated summaries to avoid regenerating
const summaryCache = new Map()

/**
 * Generate a smart summary for a thread based on its content
 */
export function generateThreadSummary(thread) {
  // Check cache first
  const cacheKey = `${thread.id}-${thread.updated_at}`
  if (summaryCache.has(cacheKey)) {
    return summaryCache.get(cacheKey)
  }

  let summary = generateLocalSummary(thread)
  
  // Cache the result
  summaryCache.set(cacheKey, summary)
  
  return summary
}

/**
 * Generate summary using local analysis
 */
function generateLocalSummary(thread) {
  if (!thread.messages || thread.messages.length === 0) {
    // Use thread description if available, otherwise generic message
    if (thread.description && thread.description.trim()) {
      return thread.description.length > 80
        ? thread.description.substring(0, 80) + '...'
        : thread.description
    }
    return "New conversation - no messages yet"
  }

  // Get key messages for analysis
  const userMessages = thread.messages.filter(msg => 
    msg.author === 'User' || msg.author === 'TestUser'
  )
  
  const agentMessages = thread.messages.filter(msg => 
    msg.author !== 'User' && msg.author !== 'TestUser'
  )

  // Analyze conversation patterns
  const analysis = analyzeConversationContent(userMessages, agentMessages)
  
  return analysis.summary
}

/**
 * Analyze conversation content to generate insights
 */
function analyzeConversationContent(userMessages, agentMessages) {
  const allText = [...userMessages, ...agentMessages]
    .map(msg => msg.content.toLowerCase())
    .join(' ')

  // Extract mentioned agents
  const mentionedAgents = extractMentionedAgents(userMessages)
  
  // Topic detection based on keywords
  const topics = detectTopics(allText)
  
  // Generate summary based on analysis
  let summary = generateSummaryFromTopics(topics, mentionedAgents, userMessages)
  
  return {
    summary,
    topics,
    mentionedAgents,
    messageCount: userMessages.length + agentMessages.length
  }
}

/**
 * Extract which agents were mentioned in the conversation
 */
function extractMentionedAgents(userMessages) {
  const agents = new Set()
  const agentPattern = /@(Researcher|Analyst|Summarizer|Writer|Coder|Translator)/gi
  
  userMessages.forEach(msg => {
    const matches = msg.content.match(agentPattern)
    if (matches) {
      matches.forEach(match => {
        agents.add(match.substring(1)) // Remove @ symbol
      })
    }
  })
  
  return Array.from(agents)
}

/**
 * Detect conversation topics based on keywords
 */
function detectTopics(text) {
  const topicKeywords = {
    'Trade & Economics': ['tariff', 'trade', 'economic', 'economy', 'market', 'business', 'finance', 'revenue', 'cost'],
    'Technology & Programming': ['code', 'programming', 'software', 'development', 'api', 'database', 'algorithm', 'tech'],
    'Research & Analysis': ['research', 'analysis', 'data', 'study', 'investigate', 'findings', 'insights', 'trends'],
    'Content & Writing': ['write', 'content', 'article', 'blog', 'document', 'text', 'writing', 'draft'],
    'File Management': ['file', 'upload', 'document', 'pdf', 'download', 'attachment', 'export'],
    'Testing & QA': ['test', 'testing', 'functionality', 'feature', 'bug', 'quality', 'validation'],
    'Translation & Language': ['translate', 'translation', 'language', 'multilingual', 'localization'],
    'Strategy & Planning': ['strategy', 'plan', 'planning', 'roadmap', 'goals', 'objectives', 'recommendations']
  }

  const detectedTopics = []
  
  for (const [topic, keywords] of Object.entries(topicKeywords)) {
    const matchCount = keywords.filter(keyword => text.includes(keyword)).length
    if (matchCount >= 2) { // Require at least 2 keyword matches
      detectedTopics.push({
        topic,
        relevance: matchCount / keywords.length,
        matchedKeywords: keywords.filter(keyword => text.includes(keyword))
      })
    }
  }
  
  // Sort by relevance
  return detectedTopics.sort((a, b) => b.relevance - a.relevance)
}

/**
 * Generate summary text based on detected topics and agents
 */
function generateSummaryFromTopics(topics, mentionedAgents, userMessages) {
  // If no clear topics detected, use first user message
  if (topics.length === 0) {
    if (userMessages.length > 0) {
      const firstMessage = userMessages[0].content
        .replace(/@\w+\s*/g, '') // Remove @mentions
        .trim()
      
      if (firstMessage.length > 80) {
        return firstMessage.substring(0, 80) + '...'
      }
      return firstMessage || 'General conversation'
    }
    return 'New conversation'
  }

  const primaryTopic = topics[0]
  let summary = ''

  // Create summary based on primary topic and agents
  switch (primaryTopic.topic) {
    case 'Trade & Economics':
      summary = `Economic discussion about ${primaryTopic.matchedKeywords.slice(0, 2).join(' and ')}`
      break
    case 'Technology & Programming':
      summary = `Technical discussion covering ${primaryTopic.matchedKeywords.slice(0, 2).join(' and ')}`
      break
    case 'Research & Analysis':
      summary = `Research session analyzing ${primaryTopic.matchedKeywords.slice(0, 2).join(' and ')}`
      break
    case 'Content & Writing':
      summary = `Content creation focused on ${primaryTopic.matchedKeywords.slice(0, 2).join(' and ')}`
      break
    case 'File Management':
      summary = `File handling and document ${primaryTopic.matchedKeywords.slice(0, 2).join(' and ')}`
      break
    case 'Testing & QA':
      summary = `Testing and validation of ${primaryTopic.matchedKeywords.slice(0, 2).join(' and ')}`
      break
    case 'Translation & Language':
      summary = `Language and translation work involving ${primaryTopic.matchedKeywords.slice(0, 2).join(' and ')}`
      break
    case 'Strategy & Planning':
      summary = `Strategic planning discussion about ${primaryTopic.matchedKeywords.slice(0, 2).join(' and ')}`
      break
    default:
      summary = `Discussion about ${primaryTopic.topic.toLowerCase()}`
  }

  // Add agent information if relevant
  if (mentionedAgents.length > 0) {
    if (mentionedAgents.length === 1) {
      summary += ` with ${mentionedAgents[0]}`
    } else if (mentionedAgents.length === 2) {
      summary += ` with ${mentionedAgents.join(' and ')}`
    } else {
      summary += ` with ${mentionedAgents.slice(0, 2).join(', ')} and ${mentionedAgents.length - 2} other agents`
    }
  }

  return summary
}

/**
 * Get conversation statistics
 */
export function getConversationStats(thread) {
  if (!thread.messages || thread.messages.length === 0) {
    return {
      totalMessages: 0,
      userMessages: 0,
      agentMessages: 0,
      uniqueAgents: 0,
      topics: []
    }
  }

  const userMessages = thread.messages.filter(msg => 
    msg.author === 'User' || msg.author === 'TestUser'
  )
  
  const agentMessages = thread.messages.filter(msg => 
    msg.author !== 'User' && msg.author !== 'TestUser'
  )

  const uniqueAgents = new Set(agentMessages.map(msg => msg.author))
  
  const allText = thread.messages.map(msg => msg.content.toLowerCase()).join(' ')
  const topics = detectTopics(allText)

  return {
    totalMessages: thread.messages.length,
    userMessages: userMessages.length,
    agentMessages: agentMessages.length,
    uniqueAgents: uniqueAgents.size,
    topics: topics.slice(0, 3) // Top 3 topics
  }
}

/**
 * Clear summary cache (useful when thread is updated)
 */
export function clearSummaryCache(threadId = null) {
  if (threadId) {
    // Clear cache for specific thread
    for (const key of summaryCache.keys()) {
      if (key.startsWith(`${threadId}-`)) {
        summaryCache.delete(key)
      }
    }
  } else {
    // Clear entire cache
    summaryCache.clear()
  }
}
