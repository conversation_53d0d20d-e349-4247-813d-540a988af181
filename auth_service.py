"""
Simple authentication service for user management.
"""
import os
from datetime import datetime, timedelta
from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from passlib.context import CryptContext
from jose import JW<PERSON>rror, jwt
from sqlalchemy.orm import Session

from models import User
from database import get_db

# Security configuration
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# HTTP Bearer token scheme
security = HTTPBearer(auto_error=False)


class AuthService:
    """Authentication service for user management."""
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash."""
        return pwd_context.verify(plain_password, hashed_password)
    
    @staticmethod
    def get_password_hash(password: str) -> str:
        """Hash a password."""
        return pwd_context.hash(password)
    
    @staticmethod
    def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
        """Create a JWT access token."""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt
    
    @staticmethod
    def verify_token(token: str) -> Optional[dict]:
        """Verify and decode a JWT token."""
        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            return payload
        except JWTError:
            return None
    
    @classmethod
    def authenticate_user(cls, db: Session, username: str, password: str) -> Optional[User]:
        """Authenticate a user with username and password."""
        user = db.query(User).filter(User.username == username).first()
        if not user:
            return None
        
        # For demo purposes, we'll use a simple password check
        # In production, you'd store hashed passwords
        if password == "demo123":  # Simple demo password
            return user
        
        return None
    
    @classmethod
    def get_user_by_username(cls, db: Session, username: str) -> Optional[User]:
        """Get user by username."""
        return db.query(User).filter(User.username == username).first()
    
    @classmethod
    def create_user(cls, db: Session, username: str, email: str = None, full_name: str = None) -> User:
        """Create a new user."""
        # Check if user already exists
        existing_user = cls.get_user_by_username(db, username)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already registered"
            )
        
        # Create new user
        user = User(
            username=username,
            email=email,
            full_name=full_name
        )
        
        db.add(user)
        db.commit()
        db.refresh(user)
        
        return user


# Dependency functions
async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db)
) -> Optional[User]:
    """
    Get current user from JWT token.
    Returns None if no valid token is provided (for optional authentication).
    """
    if not credentials:
        return None
    
    token = credentials.credentials
    payload = AuthService.verify_token(token)
    
    if not payload:
        return None
    
    username = payload.get("sub")
    if not username:
        return None
    
    user = AuthService.get_user_by_username(db, username)
    return user


async def get_current_user_required(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """
    Get current user from JWT token (required).
    Raises HTTPException if no valid token is provided.
    """
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    token = credentials.credentials
    payload = AuthService.verify_token(token)
    
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    username = payload.get("sub")
    if not username:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user = AuthService.get_user_by_username(db, username)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user


def create_demo_users(db: Session):
    """Create demo users for testing."""
    demo_users = [
        {"username": "demo", "email": "<EMAIL>", "full_name": "Demo User"},
        {"username": "alice", "email": "<EMAIL>", "full_name": "Alice Johnson"},
        {"username": "bob", "email": "<EMAIL>", "full_name": "Bob Smith"}
    ]
    
    for user_data in demo_users:
        existing_user = AuthService.get_user_by_username(db, user_data["username"])
        if not existing_user:
            try:
                AuthService.create_user(db, **user_data)
                print(f"✅ Created demo user: {user_data['username']}")
            except Exception as e:
                print(f"❌ Failed to create demo user {user_data['username']}: {e}")
        else:
            print(f"ℹ️  Demo user already exists: {user_data['username']}")
