"""
Test script specifically for the new agents.
"""
import requests
import time

BASE_URL = "http://localhost:8000"

def test_new_agents():
    """Test the new agents specifically."""
    print("🧪 Testing New Agents")
    print("=" * 50)
    
    # Create a fresh thread
    thread_data = {"title": "New Agents Test", "description": "Testing Writer, Coder, Translator"}
    response = requests.post(f"{BASE_URL}/api/threads", json=thread_data)
    
    if response.status_code != 201:
        print(f"❌ Failed to create thread: {response.status_code}")
        return
    
    thread_id = response.json()["id"]
    print(f"✅ Created test thread (ID: {thread_id})")
    
    # Test each new agent
    test_cases = [
        {
            "agent": "@Writer",
            "message": "@Writer Write a short product description for an AI-powered chatbot",
            "expected_keywords": ["chatbot", "AI", "intelligent", "conversation"]
        },
        {
            "agent": "@Coder", 
            "message": "@Coder Create a Python function to reverse a string",
            "expected_keywords": ["def", "return", "string", "reverse", "python"]
        },
        {
            "agent": "@Translator",
            "message": "@Translator Translate 'Good morning' to Spanish, French, and German",
            "expected_keywords": ["spanish", "french", "german", "buenos", "bonjour", "guten"]
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🔍 Testing {test_case['agent']}...")
        
        # Send message
        message_data = {
            "content": test_case["message"],
            "author": "TestUser"
        }
        
        response = requests.post(f"{BASE_URL}/api/threads/{thread_id}/messages", json=message_data)
        
        if response.status_code == 200:
            result = response.json()
            agent_name = result["author"]
            content = result["content"].lower()
            
            print(f"✅ Response from: {agent_name}")
            print(f"📝 Content preview: {result['content'][:100]}...")
            
            # Check if response contains expected keywords
            found_keywords = [kw for kw in test_case["expected_keywords"] if kw.lower() in content]
            
            if found_keywords:
                print(f"✅ Found relevant keywords: {found_keywords}")
            else:
                print(f"⚠️  No expected keywords found. Expected: {test_case['expected_keywords']}")
                
            # Verify correct agent responded
            expected_agent = test_case["agent"].replace("@", "")
            if agent_name == expected_agent:
                print(f"✅ Correct agent responded: {agent_name}")
            else:
                print(f"❌ Wrong agent responded. Expected: {expected_agent}, Got: {agent_name}")
                
        else:
            print(f"❌ Failed to send message: {response.status_code}")
            print(f"   Error: {response.text}")
        
        time.sleep(1)  # Brief pause between requests
    
    print(f"\n🎉 Test completed for thread {thread_id}")

if __name__ == "__main__":
    try:
        test_new_agents()
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Make sure the server is running at http://localhost:8000")
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
