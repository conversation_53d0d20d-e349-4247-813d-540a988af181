"""
Conversation export service for various formats.
"""
import json
import csv
from datetime import datetime
from typing import List, Dict, Any
from io import String<PERSON>, BytesIO
from fastapi import HTT<PERSON>Exception
from fastapi.responses import StreamingResponse

from models import Thread, Message


class ConversationExporter:
    """Service for exporting conversations in various formats."""
    
    @staticmethod
    def format_message_for_export(message: Message) -> Dict[str, Any]:
        """Format a message for export."""
        return {
            'id': message.id,
            'content': message.content,
            'author': message.author,
            'author_type': message.author_type,
            'created_at': message.created_at.isoformat(),
            'timestamp': message.created_at.strftime('%Y-%m-%d %H:%M:%S')
        }
    
    @staticmethod
    def format_thread_metadata(thread: Thread) -> Dict[str, Any]:
        """Format thread metadata for export."""
        return {
            'id': thread.id,
            'title': thread.title,
            'description': thread.description,
            'created_at': thread.created_at.isoformat(),
            'updated_at': thread.updated_at.isoformat(),
            'message_count': len(thread.messages) if thread.messages else 0,
            'participants': [p.username for p in thread.participants] if thread.participants else [],
            'tags': [t.name for t in thread.tags] if thread.tags else []
        }
    
    @classmethod
    def export_to_json(cls, thread: Thread) -> str:
        """Export conversation to JSON format."""
        messages = [cls.format_message_for_export(msg) for msg in thread.messages] if thread.messages else []
        
        export_data = {
            'thread': cls.format_thread_metadata(thread),
            'messages': messages,
            'export_info': {
                'format': 'json',
                'exported_at': datetime.now().isoformat(),
                'version': '1.0'
            }
        }
        
        return json.dumps(export_data, indent=2, ensure_ascii=False)
    
    @classmethod
    def export_to_csv(cls, thread: Thread) -> str:
        """Export conversation to CSV format."""
        output = StringIO()
        writer = csv.writer(output)
        
        # Write header
        writer.writerow([
            'Message ID', 'Timestamp', 'Author', 'Author Type', 'Content'
        ])
        
        # Write messages
        if thread.messages:
            for message in thread.messages:
                writer.writerow([
                    message.id,
                    message.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    message.author,
                    message.author_type,
                    message.content.replace('\n', ' ').replace('\r', '')  # Clean newlines for CSV
                ])
        
        return output.getvalue()
    
    @classmethod
    def export_to_markdown(cls, thread: Thread) -> str:
        """Export conversation to Markdown format."""
        lines = []
        
        # Thread header
        lines.append(f"# {thread.title}")
        lines.append("")
        
        if thread.description:
            lines.append(f"**Description:** {thread.description}")
            lines.append("")
        
        # Metadata
        lines.append("## Conversation Details")
        lines.append("")
        lines.append(f"- **Created:** {thread.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append(f"- **Updated:** {thread.updated_at.strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append(f"- **Messages:** {len(thread.messages) if thread.messages else 0}")
        
        if thread.participants:
            participants = ", ".join([p.username for p in thread.participants])
            lines.append(f"- **Participants:** {participants}")
        
        if thread.tags:
            tags = ", ".join([t.name for t in thread.tags])
            lines.append(f"- **Tags:** {tags}")
        
        lines.append("")
        lines.append("---")
        lines.append("")
        
        # Messages
        lines.append("## Conversation")
        lines.append("")
        
        if thread.messages:
            for message in thread.messages:
                timestamp = message.created_at.strftime('%H:%M:%S')
                
                # Format based on author type
                if message.author_type == 'human':
                    lines.append(f"### 👤 {message.author} ({timestamp})")
                else:
                    # Agent message with appropriate emoji
                    agent_emojis = {
                        'Researcher': '🔍',
                        'Analyst': '📊',
                        'Summarizer': '📝',
                        'Writer': '✍️',
                        'Coder': '💻',
                        'Translator': '🌍',
                        'System': '🤖'
                    }
                    emoji = agent_emojis.get(message.author, '🤖')
                    lines.append(f"### {emoji} {message.author} ({timestamp})")
                
                lines.append("")
                lines.append(message.content)
                lines.append("")
                lines.append("---")
                lines.append("")
        else:
            lines.append("*No messages in this conversation.*")
        
        # Footer
        lines.append("")
        lines.append(f"*Exported on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} from Collaborative Intelligence Platform*")
        
        return "\n".join(lines)
    
    @classmethod
    def export_to_txt(cls, thread: Thread) -> str:
        """Export conversation to plain text format."""
        lines = []
        
        # Header
        lines.append("=" * 60)
        lines.append(f"CONVERSATION: {thread.title}")
        lines.append("=" * 60)
        lines.append("")
        
        if thread.description:
            lines.append(f"Description: {thread.description}")
            lines.append("")
        
        lines.append(f"Created: {thread.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append(f"Updated: {thread.updated_at.strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append(f"Messages: {len(thread.messages) if thread.messages else 0}")
        lines.append("")
        lines.append("-" * 60)
        lines.append("")
        
        # Messages
        if thread.messages:
            for i, message in enumerate(thread.messages, 1):
                timestamp = message.created_at.strftime('%Y-%m-%d %H:%M:%S')
                lines.append(f"[{i}] {message.author} ({timestamp})")
                lines.append("")
                lines.append(message.content)
                lines.append("")
                lines.append("-" * 40)
                lines.append("")
        else:
            lines.append("No messages in this conversation.")
        
        lines.append("")
        lines.append(f"Exported on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append("Collaborative Intelligence Platform")
        
        return "\n".join(lines)


def create_export_response(content: str, filename: str, content_type: str) -> StreamingResponse:
    """Create a streaming response for file download."""
    
    def generate():
        yield content.encode('utf-8')
    
    headers = {
        'Content-Disposition': f'attachment; filename="{filename}"'
    }
    
    return StreamingResponse(
        generate(),
        media_type=content_type,
        headers=headers
    )


def export_conversation(thread: Thread, format_type: str) -> StreamingResponse:
    """
    Export conversation in the specified format.
    
    Args:
        thread: Thread object to export
        format_type: Export format ('json', 'csv', 'markdown', 'txt')
    
    Returns:
        StreamingResponse with the exported content
    
    Raises:
        HTTPException: If format is not supported
    """
    exporter = ConversationExporter()
    
    # Generate safe filename
    safe_title = "".join(c for c in thread.title if c.isalnum() or c in (' ', '-', '_')).rstrip()
    safe_title = safe_title.replace(' ', '_')
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    if format_type == 'json':
        content = exporter.export_to_json(thread)
        filename = f"{safe_title}_{timestamp}.json"
        content_type = "application/json"
    
    elif format_type == 'csv':
        content = exporter.export_to_csv(thread)
        filename = f"{safe_title}_{timestamp}.csv"
        content_type = "text/csv"
    
    elif format_type == 'markdown':
        content = exporter.export_to_markdown(thread)
        filename = f"{safe_title}_{timestamp}.md"
        content_type = "text/markdown"
    
    elif format_type == 'txt':
        content = exporter.export_to_txt(thread)
        filename = f"{safe_title}_{timestamp}.txt"
        content_type = "text/plain"
    
    else:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported export format: {format_type}. Supported formats: json, csv, markdown, txt"
        )
    
    return create_export_response(content, filename, content_type)
