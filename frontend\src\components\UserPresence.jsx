/**
 * User presence component showing online users and typing indicators.
 */
import React, { useState, useEffect } from 'react'
import authService from '../services/authService'

const UserPresence = ({ threadId, websocket }) => {
  const [onlineUsers, setOnlineUsers] = useState(new Set())
  const [typingUsers, setTypingUsers] = useState(new Set())
  const [recentJoins, setRecentJoins] = useState(new Set())

  useEffect(() => {
    if (!websocket) return

    const handleMessage = (event) => {
      try {
        const message = JSON.parse(event.data)
        
        switch (message.type) {
          case 'user_joined':
            const joinedUser = message.data.user
            if (joinedUser) {
              setOnlineUsers(prev => new Set([...prev, joinedUser]))
              setRecentJoins(prev => new Set([...prev, joinedUser]))
              
              // Remove from recent joins after 3 seconds
              setTimeout(() => {
                setRecentJoins(prev => {
                  const newSet = new Set(prev)
                  newSet.delete(joinedUser)
                  return newSet
                })
              }, 3000)
            }
            break
            
          case 'user_left':
            const leftUser = message.data.user
            if (leftUser) {
              setOnlineUsers(prev => {
                const newSet = new Set(prev)
                newSet.delete(leftUser)
                return newSet
              })
              setTypingUsers(prev => {
                const newSet = new Set(prev)
                newSet.delete(leftUser)
                return newSet
              })
            }
            break
            
          case 'user_typing':
            const typingUser = message.data.user
            const isTyping = message.data.is_typing
            
            if (typingUser && typingUser !== authService.getCurrentUser()?.username) {
              setTypingUsers(prev => {
                const newSet = new Set(prev)
                if (isTyping) {
                  newSet.add(typingUser)
                } else {
                  newSet.delete(typingUser)
                }
                return newSet
              })
            }
            break
            
          case 'system':
            // Handle system messages that might contain user info
            if (message.data.user) {
              setOnlineUsers(prev => new Set([...prev, message.data.user]))
            }
            break
        }
      } catch (error) {
        console.error('Error handling presence message:', error)
      }
    }

    websocket.addEventListener('message', handleMessage)
    
    return () => {
      websocket.removeEventListener('message', handleMessage)
    }
  }, [websocket])

  const getUserDisplayInfo = (username) => {
    return authService.getUserDisplayInfo(username)
  }

  const currentUser = authService.getCurrentUser()
  const otherUsers = Array.from(onlineUsers).filter(user => user !== currentUser?.username)
  const typingUsersArray = Array.from(typingUsers)

  if (otherUsers.length === 0 && typingUsersArray.length === 0) {
    return null
  }

  return (
    <div className="bg-gray-50 border-b border-gray-200 px-4 py-2">
      <div className="flex items-center justify-between">
        {/* Online Users */}
        {otherUsers.length > 0 && (
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-600">Online:</span>
            </div>
            
            <div className="flex items-center space-x-2">
              {otherUsers.slice(0, 5).map((username) => {
                const userInfo = getUserDisplayInfo(username)
                const isRecentJoin = recentJoins.has(username)
                
                return (
                  <div
                    key={username}
                    className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs transition-all duration-300 ${
                      isRecentJoin 
                        ? 'bg-green-100 border border-green-300 animate-pulse' 
                        : 'bg-white border border-gray-200'
                    }`}
                  >
                    <div
                      className="w-4 h-4 rounded-full flex items-center justify-center text-white text-xs font-medium"
                      style={{ backgroundColor: userInfo.color }}
                    >
                      {userInfo.displayName.charAt(0)}
                    </div>
                    <span className="text-gray-700">{userInfo.displayName}</span>
                    {isRecentJoin && (
                      <span className="text-green-600 text-xs">joined</span>
                    )}
                  </div>
                )
              })}
              
              {otherUsers.length > 5 && (
                <div className="px-2 py-1 bg-gray-100 rounded-full text-xs text-gray-600">
                  +{otherUsers.length - 5} more
                </div>
              )}
            </div>
          </div>
        )}

        {/* Typing Indicators */}
        {typingUsersArray.length > 0 && (
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1">
              {typingUsersArray.slice(0, 3).map((username) => {
                const userInfo = getUserDisplayInfo(username)
                return (
                  <div
                    key={username}
                    className="w-4 h-4 rounded-full flex items-center justify-center text-white text-xs font-medium"
                    style={{ backgroundColor: userInfo.color }}
                  >
                    {userInfo.displayName.charAt(0)}
                  </div>
                )
              })}
            </div>
            
            <div className="flex items-center space-x-1">
              <div className="flex space-x-1">
                <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce"></div>
                <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
              
              <span className="text-xs text-gray-600">
                {typingUsersArray.length === 1 
                  ? `${getUserDisplayInfo(typingUsersArray[0]).displayName} is typing...`
                  : typingUsersArray.length === 2
                  ? `${getUserDisplayInfo(typingUsersArray[0]).displayName} and ${getUserDisplayInfo(typingUsersArray[1]).displayName} are typing...`
                  : `${typingUsersArray.length} people are typing...`
                }
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default UserPresence
