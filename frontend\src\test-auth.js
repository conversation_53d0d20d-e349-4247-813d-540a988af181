/**
 * Simple test to verify authService works without process.env issues
 */

// Test the authService import
try {
  console.log('Testing authService import...');
  
  // This should work now without process.env errors
  const authService = require('./services/authService.js');
  console.log('✅ authService imported successfully');
  console.log('authService methods:', Object.getOwnPropertyNames(authService.default));
  
} catch (error) {
  console.error('❌ authService import failed:', error.message);
}

// Test API URL
try {
  const API_BASE_URL = 'http://localhost:8000';
  console.log('✅ API_BASE_URL set to:', API_BASE_URL);
} catch (error) {
  console.error('❌ API_BASE_URL error:', error.message);
}

console.log('Test completed. If no errors above, the fix should work.');
